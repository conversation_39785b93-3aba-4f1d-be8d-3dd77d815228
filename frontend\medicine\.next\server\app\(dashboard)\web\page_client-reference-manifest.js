globalThis.__RSC_MANIFEST=(globalThis.__RSC_MANIFEST||{});globalThis.__RSC_MANIFEST["/(dashboard)/web/page"]={"moduleLoading":{"prefix":"/_next/"},"ssrModuleMapping":{"(app-pages-browser)/./node_modules/@mantine/core/esm/components/Accordion/Accordion.mjs":{"*":{"id":"(ssr)/./node_modules/@mantine/core/esm/components/Accordion/Accordion.mjs","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@mantine/core/esm/components/Accordion/AccordionChevron.mjs":{"*":{"id":"(ssr)/./node_modules/@mantine/core/esm/components/Accordion/AccordionChevron.mjs","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@mantine/core/esm/components/Accordion/AccordionControl/AccordionControl.mjs":{"*":{"id":"(ssr)/./node_modules/@mantine/core/esm/components/Accordion/AccordionControl/AccordionControl.mjs","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@mantine/core/esm/components/Accordion/AccordionItem/AccordionItem.mjs":{"*":{"id":"(ssr)/./node_modules/@mantine/core/esm/components/Accordion/AccordionItem/AccordionItem.mjs","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@mantine/core/esm/components/Accordion/AccordionPanel/AccordionPanel.mjs":{"*":{"id":"(ssr)/./node_modules/@mantine/core/esm/components/Accordion/AccordionPanel/AccordionPanel.mjs","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@mantine/core/esm/components/ActionIcon/ActionIcon.mjs":{"*":{"id":"(ssr)/./node_modules/@mantine/core/esm/components/ActionIcon/ActionIcon.mjs","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@mantine/core/esm/components/ActionIcon/ActionIconGroup/ActionIconGroup.mjs":{"*":{"id":"(ssr)/./node_modules/@mantine/core/esm/components/ActionIcon/ActionIconGroup/ActionIconGroup.mjs","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@mantine/core/esm/components/ActionIcon/ActionIconGroupSection/ActionIconGroupSection.mjs":{"*":{"id":"(ssr)/./node_modules/@mantine/core/esm/components/ActionIcon/ActionIconGroupSection/ActionIconGroupSection.mjs","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@mantine/core/esm/components/Affix/Affix.mjs":{"*":{"id":"(ssr)/./node_modules/@mantine/core/esm/components/Affix/Affix.mjs","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@mantine/core/esm/components/Alert/Alert.mjs":{"*":{"id":"(ssr)/./node_modules/@mantine/core/esm/components/Alert/Alert.mjs","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@mantine/core/esm/components/Anchor/Anchor.mjs":{"*":{"id":"(ssr)/./node_modules/@mantine/core/esm/components/Anchor/Anchor.mjs","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@mantine/core/esm/components/AngleSlider/AngleSlider.mjs":{"*":{"id":"(ssr)/./node_modules/@mantine/core/esm/components/AngleSlider/AngleSlider.mjs","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@mantine/core/esm/components/AppShell/AppShell.mjs":{"*":{"id":"(ssr)/./node_modules/@mantine/core/esm/components/AppShell/AppShell.mjs","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@mantine/core/esm/components/AppShell/AppShellAside/AppShellAside.mjs":{"*":{"id":"(ssr)/./node_modules/@mantine/core/esm/components/AppShell/AppShellAside/AppShellAside.mjs","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@mantine/core/esm/components/AppShell/AppShellFooter/AppShellFooter.mjs":{"*":{"id":"(ssr)/./node_modules/@mantine/core/esm/components/AppShell/AppShellFooter/AppShellFooter.mjs","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@mantine/core/esm/components/AppShell/AppShellHeader/AppShellHeader.mjs":{"*":{"id":"(ssr)/./node_modules/@mantine/core/esm/components/AppShell/AppShellHeader/AppShellHeader.mjs","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@mantine/core/esm/components/AppShell/AppShellMain/AppShellMain.mjs":{"*":{"id":"(ssr)/./node_modules/@mantine/core/esm/components/AppShell/AppShellMain/AppShellMain.mjs","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@mantine/core/esm/components/AppShell/AppShellNavbar/AppShellNavbar.mjs":{"*":{"id":"(ssr)/./node_modules/@mantine/core/esm/components/AppShell/AppShellNavbar/AppShellNavbar.mjs","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@mantine/core/esm/components/AppShell/AppShellSection/AppShellSection.mjs":{"*":{"id":"(ssr)/./node_modules/@mantine/core/esm/components/AppShell/AppShellSection/AppShellSection.mjs","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@mantine/core/esm/components/AspectRatio/AspectRatio.mjs":{"*":{"id":"(ssr)/./node_modules/@mantine/core/esm/components/AspectRatio/AspectRatio.mjs","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@mantine/core/esm/components/Autocomplete/Autocomplete.mjs":{"*":{"id":"(ssr)/./node_modules/@mantine/core/esm/components/Autocomplete/Autocomplete.mjs","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@mantine/core/esm/components/Avatar/Avatar.mjs":{"*":{"id":"(ssr)/./node_modules/@mantine/core/esm/components/Avatar/Avatar.mjs","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@mantine/core/esm/components/Avatar/AvatarGroup/AvatarGroup.mjs":{"*":{"id":"(ssr)/./node_modules/@mantine/core/esm/components/Avatar/AvatarGroup/AvatarGroup.mjs","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@mantine/core/esm/components/BackgroundImage/BackgroundImage.mjs":{"*":{"id":"(ssr)/./node_modules/@mantine/core/esm/components/BackgroundImage/BackgroundImage.mjs","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@mantine/core/esm/components/Badge/Badge.mjs":{"*":{"id":"(ssr)/./node_modules/@mantine/core/esm/components/Badge/Badge.mjs","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@mantine/core/esm/components/Blockquote/Blockquote.mjs":{"*":{"id":"(ssr)/./node_modules/@mantine/core/esm/components/Blockquote/Blockquote.mjs","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@mantine/core/esm/components/Breadcrumbs/Breadcrumbs.mjs":{"*":{"id":"(ssr)/./node_modules/@mantine/core/esm/components/Breadcrumbs/Breadcrumbs.mjs","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@mantine/core/esm/components/Burger/Burger.mjs":{"*":{"id":"(ssr)/./node_modules/@mantine/core/esm/components/Burger/Burger.mjs","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@mantine/core/esm/components/Button/Button.mjs":{"*":{"id":"(ssr)/./node_modules/@mantine/core/esm/components/Button/Button.mjs","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@mantine/core/esm/components/Button/ButtonGroup/ButtonGroup.mjs":{"*":{"id":"(ssr)/./node_modules/@mantine/core/esm/components/Button/ButtonGroup/ButtonGroup.mjs","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@mantine/core/esm/components/Button/ButtonGroupSection/ButtonGroupSection.mjs":{"*":{"id":"(ssr)/./node_modules/@mantine/core/esm/components/Button/ButtonGroupSection/ButtonGroupSection.mjs","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@mantine/core/esm/components/Card/Card.mjs":{"*":{"id":"(ssr)/./node_modules/@mantine/core/esm/components/Card/Card.mjs","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@mantine/core/esm/components/Card/CardSection/CardSection.mjs":{"*":{"id":"(ssr)/./node_modules/@mantine/core/esm/components/Card/CardSection/CardSection.mjs","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@mantine/core/esm/components/Center/Center.mjs":{"*":{"id":"(ssr)/./node_modules/@mantine/core/esm/components/Center/Center.mjs","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@mantine/core/esm/components/Checkbox/Checkbox.mjs":{"*":{"id":"(ssr)/./node_modules/@mantine/core/esm/components/Checkbox/Checkbox.mjs","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@mantine/core/esm/components/Checkbox/CheckboxCard/CheckboxCard.context.mjs":{"*":{"id":"(ssr)/./node_modules/@mantine/core/esm/components/Checkbox/CheckboxCard/CheckboxCard.context.mjs","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@mantine/core/esm/components/Checkbox/CheckboxCard/CheckboxCard.mjs":{"*":{"id":"(ssr)/./node_modules/@mantine/core/esm/components/Checkbox/CheckboxCard/CheckboxCard.mjs","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@mantine/core/esm/components/Checkbox/CheckboxGroup.context.mjs":{"*":{"id":"(ssr)/./node_modules/@mantine/core/esm/components/Checkbox/CheckboxGroup.context.mjs","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@mantine/core/esm/components/Checkbox/CheckboxGroup/CheckboxGroup.mjs":{"*":{"id":"(ssr)/./node_modules/@mantine/core/esm/components/Checkbox/CheckboxGroup/CheckboxGroup.mjs","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@mantine/core/esm/components/Checkbox/CheckboxIndicator/CheckboxIndicator.mjs":{"*":{"id":"(ssr)/./node_modules/@mantine/core/esm/components/Checkbox/CheckboxIndicator/CheckboxIndicator.mjs","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@mantine/core/esm/components/Checkbox/CheckIcon.mjs":{"*":{"id":"(ssr)/./node_modules/@mantine/core/esm/components/Checkbox/CheckIcon.mjs","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@mantine/core/esm/components/Chip/Chip.mjs":{"*":{"id":"(ssr)/./node_modules/@mantine/core/esm/components/Chip/Chip.mjs","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@mantine/core/esm/components/Chip/ChipGroup/ChipGroup.mjs":{"*":{"id":"(ssr)/./node_modules/@mantine/core/esm/components/Chip/ChipGroup/ChipGroup.mjs","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@mantine/core/esm/components/CloseButton/CloseButton.mjs":{"*":{"id":"(ssr)/./node_modules/@mantine/core/esm/components/CloseButton/CloseButton.mjs","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@mantine/core/esm/components/CloseButton/CloseIcon.mjs":{"*":{"id":"(ssr)/./node_modules/@mantine/core/esm/components/CloseButton/CloseIcon.mjs","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@mantine/core/esm/components/Code/Code.mjs":{"*":{"id":"(ssr)/./node_modules/@mantine/core/esm/components/Code/Code.mjs","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@mantine/core/esm/components/Collapse/Collapse.mjs":{"*":{"id":"(ssr)/./node_modules/@mantine/core/esm/components/Collapse/Collapse.mjs","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@mantine/core/esm/components/ColorInput/ColorInput.mjs":{"*":{"id":"(ssr)/./node_modules/@mantine/core/esm/components/ColorInput/ColorInput.mjs","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@mantine/core/esm/components/ColorPicker/AlphaSlider/AlphaSlider.mjs":{"*":{"id":"(ssr)/./node_modules/@mantine/core/esm/components/ColorPicker/AlphaSlider/AlphaSlider.mjs","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@mantine/core/esm/components/ColorPicker/ColorPicker.mjs":{"*":{"id":"(ssr)/./node_modules/@mantine/core/esm/components/ColorPicker/ColorPicker.mjs","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@mantine/core/esm/components/ColorPicker/converters/converters.mjs":{"*":{"id":"(ssr)/./node_modules/@mantine/core/esm/components/ColorPicker/converters/converters.mjs","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@mantine/core/esm/components/ColorPicker/converters/parsers.mjs":{"*":{"id":"(ssr)/./node_modules/@mantine/core/esm/components/ColorPicker/converters/parsers.mjs","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@mantine/core/esm/components/ColorPicker/HueSlider/HueSlider.mjs":{"*":{"id":"(ssr)/./node_modules/@mantine/core/esm/components/ColorPicker/HueSlider/HueSlider.mjs","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@mantine/core/esm/components/ColorSwatch/ColorSwatch.mjs":{"*":{"id":"(ssr)/./node_modules/@mantine/core/esm/components/ColorSwatch/ColorSwatch.mjs","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@mantine/core/esm/components/Combobox/Combobox.mjs":{"*":{"id":"(ssr)/./node_modules/@mantine/core/esm/components/Combobox/Combobox.mjs","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@mantine/core/esm/components/Combobox/ComboboxChevron/ComboboxChevron.mjs":{"*":{"id":"(ssr)/./node_modules/@mantine/core/esm/components/Combobox/ComboboxChevron/ComboboxChevron.mjs","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@mantine/core/esm/components/Combobox/ComboboxClearButton/ComboboxClearButton.mjs":{"*":{"id":"(ssr)/./node_modules/@mantine/core/esm/components/Combobox/ComboboxClearButton/ComboboxClearButton.mjs","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@mantine/core/esm/components/Combobox/ComboboxDropdown/ComboboxDropdown.mjs":{"*":{"id":"(ssr)/./node_modules/@mantine/core/esm/components/Combobox/ComboboxDropdown/ComboboxDropdown.mjs","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@mantine/core/esm/components/Combobox/ComboboxDropdownTarget/ComboboxDropdownTarget.mjs":{"*":{"id":"(ssr)/./node_modules/@mantine/core/esm/components/Combobox/ComboboxDropdownTarget/ComboboxDropdownTarget.mjs","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@mantine/core/esm/components/Combobox/ComboboxEmpty/ComboboxEmpty.mjs":{"*":{"id":"(ssr)/./node_modules/@mantine/core/esm/components/Combobox/ComboboxEmpty/ComboboxEmpty.mjs","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@mantine/core/esm/components/Combobox/ComboboxEventsTarget/ComboboxEventsTarget.mjs":{"*":{"id":"(ssr)/./node_modules/@mantine/core/esm/components/Combobox/ComboboxEventsTarget/ComboboxEventsTarget.mjs","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@mantine/core/esm/components/Combobox/ComboboxFooter/ComboboxFooter.mjs":{"*":{"id":"(ssr)/./node_modules/@mantine/core/esm/components/Combobox/ComboboxFooter/ComboboxFooter.mjs","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@mantine/core/esm/components/Combobox/ComboboxGroup/ComboboxGroup.mjs":{"*":{"id":"(ssr)/./node_modules/@mantine/core/esm/components/Combobox/ComboboxGroup/ComboboxGroup.mjs","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@mantine/core/esm/components/Combobox/ComboboxHeader/ComboboxHeader.mjs":{"*":{"id":"(ssr)/./node_modules/@mantine/core/esm/components/Combobox/ComboboxHeader/ComboboxHeader.mjs","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@mantine/core/esm/components/Combobox/ComboboxHiddenInput/ComboboxHiddenInput.mjs":{"*":{"id":"(ssr)/./node_modules/@mantine/core/esm/components/Combobox/ComboboxHiddenInput/ComboboxHiddenInput.mjs","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@mantine/core/esm/components/Combobox/ComboboxOption/ComboboxOption.mjs":{"*":{"id":"(ssr)/./node_modules/@mantine/core/esm/components/Combobox/ComboboxOption/ComboboxOption.mjs","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@mantine/core/esm/components/Combobox/ComboboxOptions/ComboboxOptions.mjs":{"*":{"id":"(ssr)/./node_modules/@mantine/core/esm/components/Combobox/ComboboxOptions/ComboboxOptions.mjs","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@mantine/core/esm/components/Combobox/ComboboxSearch/ComboboxSearch.mjs":{"*":{"id":"(ssr)/./node_modules/@mantine/core/esm/components/Combobox/ComboboxSearch/ComboboxSearch.mjs","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@mantine/core/esm/components/Combobox/ComboboxTarget/ComboboxTarget.mjs":{"*":{"id":"(ssr)/./node_modules/@mantine/core/esm/components/Combobox/ComboboxTarget/ComboboxTarget.mjs","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@mantine/core/esm/components/Combobox/get-options-lockup/get-options-lockup.mjs":{"*":{"id":"(ssr)/./node_modules/@mantine/core/esm/components/Combobox/get-options-lockup/get-options-lockup.mjs","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@mantine/core/esm/components/Combobox/get-parsed-combobox-data/get-parsed-combobox-data.mjs":{"*":{"id":"(ssr)/./node_modules/@mantine/core/esm/components/Combobox/get-parsed-combobox-data/get-parsed-combobox-data.mjs","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@mantine/core/esm/components/Combobox/OptionsDropdown/default-options-filter.mjs":{"*":{"id":"(ssr)/./node_modules/@mantine/core/esm/components/Combobox/OptionsDropdown/default-options-filter.mjs","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@mantine/core/esm/components/Combobox/OptionsDropdown/is-options-group.mjs":{"*":{"id":"(ssr)/./node_modules/@mantine/core/esm/components/Combobox/OptionsDropdown/is-options-group.mjs","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@mantine/core/esm/components/Combobox/OptionsDropdown/OptionsDropdown.mjs":{"*":{"id":"(ssr)/./node_modules/@mantine/core/esm/components/Combobox/OptionsDropdown/OptionsDropdown.mjs","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@mantine/core/esm/components/Combobox/use-combobox-target-props/use-combobox-target-props.mjs":{"*":{"id":"(ssr)/./node_modules/@mantine/core/esm/components/Combobox/use-combobox-target-props/use-combobox-target-props.mjs","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@mantine/core/esm/components/Combobox/use-combobox/use-combobox.mjs":{"*":{"id":"(ssr)/./node_modules/@mantine/core/esm/components/Combobox/use-combobox/use-combobox.mjs","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@mantine/core/esm/components/Combobox/use-combobox/use-virtualized-combobox.mjs":{"*":{"id":"(ssr)/./node_modules/@mantine/core/esm/components/Combobox/use-combobox/use-virtualized-combobox.mjs","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@mantine/core/esm/components/Container/Container.mjs":{"*":{"id":"(ssr)/./node_modules/@mantine/core/esm/components/Container/Container.mjs","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@mantine/core/esm/components/CopyButton/CopyButton.mjs":{"*":{"id":"(ssr)/./node_modules/@mantine/core/esm/components/CopyButton/CopyButton.mjs","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@mantine/core/esm/components/Dialog/Dialog.mjs":{"*":{"id":"(ssr)/./node_modules/@mantine/core/esm/components/Dialog/Dialog.mjs","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@mantine/core/esm/components/Divider/Divider.mjs":{"*":{"id":"(ssr)/./node_modules/@mantine/core/esm/components/Divider/Divider.mjs","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@mantine/core/esm/components/Drawer/Drawer.mjs":{"*":{"id":"(ssr)/./node_modules/@mantine/core/esm/components/Drawer/Drawer.mjs","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@mantine/core/esm/components/Drawer/DrawerBody.mjs":{"*":{"id":"(ssr)/./node_modules/@mantine/core/esm/components/Drawer/DrawerBody.mjs","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@mantine/core/esm/components/Drawer/DrawerCloseButton.mjs":{"*":{"id":"(ssr)/./node_modules/@mantine/core/esm/components/Drawer/DrawerCloseButton.mjs","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@mantine/core/esm/components/Drawer/DrawerContent.mjs":{"*":{"id":"(ssr)/./node_modules/@mantine/core/esm/components/Drawer/DrawerContent.mjs","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@mantine/core/esm/components/Drawer/DrawerHeader.mjs":{"*":{"id":"(ssr)/./node_modules/@mantine/core/esm/components/Drawer/DrawerHeader.mjs","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@mantine/core/esm/components/Drawer/DrawerOverlay.mjs":{"*":{"id":"(ssr)/./node_modules/@mantine/core/esm/components/Drawer/DrawerOverlay.mjs","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@mantine/core/esm/components/Drawer/DrawerRoot.mjs":{"*":{"id":"(ssr)/./node_modules/@mantine/core/esm/components/Drawer/DrawerRoot.mjs","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@mantine/core/esm/components/Drawer/DrawerStack.mjs":{"*":{"id":"(ssr)/./node_modules/@mantine/core/esm/components/Drawer/DrawerStack.mjs","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@mantine/core/esm/components/Drawer/DrawerTitle.mjs":{"*":{"id":"(ssr)/./node_modules/@mantine/core/esm/components/Drawer/DrawerTitle.mjs","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@mantine/core/esm/components/Fieldset/Fieldset.mjs":{"*":{"id":"(ssr)/./node_modules/@mantine/core/esm/components/Fieldset/Fieldset.mjs","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@mantine/core/esm/components/FileButton/FileButton.mjs":{"*":{"id":"(ssr)/./node_modules/@mantine/core/esm/components/FileButton/FileButton.mjs","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@mantine/core/esm/components/FileInput/FileInput.mjs":{"*":{"id":"(ssr)/./node_modules/@mantine/core/esm/components/FileInput/FileInput.mjs","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@mantine/core/esm/components/Flex/flex-props.mjs":{"*":{"id":"(ssr)/./node_modules/@mantine/core/esm/components/Flex/flex-props.mjs","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@mantine/core/esm/components/Flex/Flex.mjs":{"*":{"id":"(ssr)/./node_modules/@mantine/core/esm/components/Flex/Flex.mjs","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@mantine/core/esm/components/Floating/FloatingArrow/FloatingArrow.mjs":{"*":{"id":"(ssr)/./node_modules/@mantine/core/esm/components/Floating/FloatingArrow/FloatingArrow.mjs","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@mantine/core/esm/components/Floating/get-floating-position/get-floating-position.mjs":{"*":{"id":"(ssr)/./node_modules/@mantine/core/esm/components/Floating/get-floating-position/get-floating-position.mjs","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@mantine/core/esm/components/Floating/use-delayed-hover.mjs":{"*":{"id":"(ssr)/./node_modules/@mantine/core/esm/components/Floating/use-delayed-hover.mjs","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@mantine/core/esm/components/Floating/use-floating-auto-update.mjs":{"*":{"id":"(ssr)/./node_modules/@mantine/core/esm/components/Floating/use-floating-auto-update.mjs","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@mantine/core/esm/components/FloatingIndicator/FloatingIndicator.mjs":{"*":{"id":"(ssr)/./node_modules/@mantine/core/esm/components/FloatingIndicator/FloatingIndicator.mjs","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@mantine/core/esm/components/FocusTrap/FocusTrap.mjs":{"*":{"id":"(ssr)/./node_modules/@mantine/core/esm/components/FocusTrap/FocusTrap.mjs","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@mantine/core/esm/components/Grid/Grid.mjs":{"*":{"id":"(ssr)/./node_modules/@mantine/core/esm/components/Grid/Grid.mjs","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@mantine/core/esm/components/Grid/GridCol/GridCol.mjs":{"*":{"id":"(ssr)/./node_modules/@mantine/core/esm/components/Grid/GridCol/GridCol.mjs","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@mantine/core/esm/components/Group/Group.mjs":{"*":{"id":"(ssr)/./node_modules/@mantine/core/esm/components/Group/Group.mjs","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@mantine/core/esm/components/Highlight/Highlight.mjs":{"*":{"id":"(ssr)/./node_modules/@mantine/core/esm/components/Highlight/Highlight.mjs","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@mantine/core/esm/components/HoverCard/HoverCard.mjs":{"*":{"id":"(ssr)/./node_modules/@mantine/core/esm/components/HoverCard/HoverCard.mjs","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@mantine/core/esm/components/HoverCard/HoverCardDropdown/HoverCardDropdown.mjs":{"*":{"id":"(ssr)/./node_modules/@mantine/core/esm/components/HoverCard/HoverCardDropdown/HoverCardDropdown.mjs","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@mantine/core/esm/components/HoverCard/HoverCardTarget/HoverCardTarget.mjs":{"*":{"id":"(ssr)/./node_modules/@mantine/core/esm/components/HoverCard/HoverCardTarget/HoverCardTarget.mjs","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@mantine/core/esm/components/Image/Image.mjs":{"*":{"id":"(ssr)/./node_modules/@mantine/core/esm/components/Image/Image.mjs","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@mantine/core/esm/components/Indicator/Indicator.mjs":{"*":{"id":"(ssr)/./node_modules/@mantine/core/esm/components/Indicator/Indicator.mjs","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@mantine/core/esm/components/Input/Input.mjs":{"*":{"id":"(ssr)/./node_modules/@mantine/core/esm/components/Input/Input.mjs","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@mantine/core/esm/components/Input/InputClearButton/InputClearButton.mjs":{"*":{"id":"(ssr)/./node_modules/@mantine/core/esm/components/Input/InputClearButton/InputClearButton.mjs","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@mantine/core/esm/components/Input/InputDescription/InputDescription.mjs":{"*":{"id":"(ssr)/./node_modules/@mantine/core/esm/components/Input/InputDescription/InputDescription.mjs","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@mantine/core/esm/components/Input/InputError/InputError.mjs":{"*":{"id":"(ssr)/./node_modules/@mantine/core/esm/components/Input/InputError/InputError.mjs","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@mantine/core/esm/components/Input/InputLabel/InputLabel.mjs":{"*":{"id":"(ssr)/./node_modules/@mantine/core/esm/components/Input/InputLabel/InputLabel.mjs","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@mantine/core/esm/components/Input/InputPlaceholder/InputPlaceholder.mjs":{"*":{"id":"(ssr)/./node_modules/@mantine/core/esm/components/Input/InputPlaceholder/InputPlaceholder.mjs","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@mantine/core/esm/components/Input/InputWrapper.context.mjs":{"*":{"id":"(ssr)/./node_modules/@mantine/core/esm/components/Input/InputWrapper.context.mjs","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@mantine/core/esm/components/Input/InputWrapper/InputWrapper.mjs":{"*":{"id":"(ssr)/./node_modules/@mantine/core/esm/components/Input/InputWrapper/InputWrapper.mjs","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@mantine/core/esm/components/Input/use-input-props.mjs":{"*":{"id":"(ssr)/./node_modules/@mantine/core/esm/components/Input/use-input-props.mjs","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@mantine/core/esm/components/InputBase/InputBase.mjs":{"*":{"id":"(ssr)/./node_modules/@mantine/core/esm/components/InputBase/InputBase.mjs","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@mantine/core/esm/components/JsonInput/JsonInput.mjs":{"*":{"id":"(ssr)/./node_modules/@mantine/core/esm/components/JsonInput/JsonInput.mjs","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@mantine/core/esm/components/Kbd/Kbd.mjs":{"*":{"id":"(ssr)/./node_modules/@mantine/core/esm/components/Kbd/Kbd.mjs","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@mantine/core/esm/components/List/List.mjs":{"*":{"id":"(ssr)/./node_modules/@mantine/core/esm/components/List/List.mjs","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@mantine/core/esm/components/List/ListItem/ListItem.mjs":{"*":{"id":"(ssr)/./node_modules/@mantine/core/esm/components/List/ListItem/ListItem.mjs","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@mantine/core/esm/components/Loader/Loader.mjs":{"*":{"id":"(ssr)/./node_modules/@mantine/core/esm/components/Loader/Loader.mjs","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@mantine/core/esm/components/LoadingOverlay/LoadingOverlay.mjs":{"*":{"id":"(ssr)/./node_modules/@mantine/core/esm/components/LoadingOverlay/LoadingOverlay.mjs","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@mantine/core/esm/components/Mark/Mark.mjs":{"*":{"id":"(ssr)/./node_modules/@mantine/core/esm/components/Mark/Mark.mjs","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@mantine/core/esm/components/Menu/Menu.mjs":{"*":{"id":"(ssr)/./node_modules/@mantine/core/esm/components/Menu/Menu.mjs","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@mantine/core/esm/components/Menu/MenuDivider/MenuDivider.mjs":{"*":{"id":"(ssr)/./node_modules/@mantine/core/esm/components/Menu/MenuDivider/MenuDivider.mjs","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@mantine/core/esm/components/Menu/MenuDropdown/MenuDropdown.mjs":{"*":{"id":"(ssr)/./node_modules/@mantine/core/esm/components/Menu/MenuDropdown/MenuDropdown.mjs","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@mantine/core/esm/components/Menu/MenuItem/MenuItem.mjs":{"*":{"id":"(ssr)/./node_modules/@mantine/core/esm/components/Menu/MenuItem/MenuItem.mjs","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@mantine/core/esm/components/Menu/MenuLabel/MenuLabel.mjs":{"*":{"id":"(ssr)/./node_modules/@mantine/core/esm/components/Menu/MenuLabel/MenuLabel.mjs","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@mantine/core/esm/components/Menu/MenuTarget/MenuTarget.mjs":{"*":{"id":"(ssr)/./node_modules/@mantine/core/esm/components/Menu/MenuTarget/MenuTarget.mjs","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@mantine/core/esm/components/Modal/Modal.mjs":{"*":{"id":"(ssr)/./node_modules/@mantine/core/esm/components/Modal/Modal.mjs","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@mantine/core/esm/components/Modal/ModalBody.mjs":{"*":{"id":"(ssr)/./node_modules/@mantine/core/esm/components/Modal/ModalBody.mjs","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@mantine/core/esm/components/Modal/ModalCloseButton.mjs":{"*":{"id":"(ssr)/./node_modules/@mantine/core/esm/components/Modal/ModalCloseButton.mjs","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@mantine/core/esm/components/Modal/ModalContent.mjs":{"*":{"id":"(ssr)/./node_modules/@mantine/core/esm/components/Modal/ModalContent.mjs","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@mantine/core/esm/components/Modal/ModalHeader.mjs":{"*":{"id":"(ssr)/./node_modules/@mantine/core/esm/components/Modal/ModalHeader.mjs","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@mantine/core/esm/components/Modal/ModalOverlay.mjs":{"*":{"id":"(ssr)/./node_modules/@mantine/core/esm/components/Modal/ModalOverlay.mjs","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@mantine/core/esm/components/Modal/ModalRoot.mjs":{"*":{"id":"(ssr)/./node_modules/@mantine/core/esm/components/Modal/ModalRoot.mjs","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@mantine/core/esm/components/Modal/ModalStack.mjs":{"*":{"id":"(ssr)/./node_modules/@mantine/core/esm/components/Modal/ModalStack.mjs","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@mantine/core/esm/components/Modal/ModalTitle.mjs":{"*":{"id":"(ssr)/./node_modules/@mantine/core/esm/components/Modal/ModalTitle.mjs","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@mantine/core/esm/components/Modal/use-modals-stack.mjs":{"*":{"id":"(ssr)/./node_modules/@mantine/core/esm/components/Modal/use-modals-stack.mjs","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@mantine/core/esm/components/ModalBase/ModalBase.mjs":{"*":{"id":"(ssr)/./node_modules/@mantine/core/esm/components/ModalBase/ModalBase.mjs","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@mantine/core/esm/components/ModalBase/ModalBaseBody.mjs":{"*":{"id":"(ssr)/./node_modules/@mantine/core/esm/components/ModalBase/ModalBaseBody.mjs","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@mantine/core/esm/components/ModalBase/ModalBaseCloseButton.mjs":{"*":{"id":"(ssr)/./node_modules/@mantine/core/esm/components/ModalBase/ModalBaseCloseButton.mjs","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@mantine/core/esm/components/ModalBase/ModalBaseContent.mjs":{"*":{"id":"(ssr)/./node_modules/@mantine/core/esm/components/ModalBase/ModalBaseContent.mjs","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@mantine/core/esm/components/ModalBase/ModalBaseHeader.mjs":{"*":{"id":"(ssr)/./node_modules/@mantine/core/esm/components/ModalBase/ModalBaseHeader.mjs","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@mantine/core/esm/components/ModalBase/ModalBaseOverlay.mjs":{"*":{"id":"(ssr)/./node_modules/@mantine/core/esm/components/ModalBase/ModalBaseOverlay.mjs","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@mantine/core/esm/components/ModalBase/ModalBaseTitle.mjs":{"*":{"id":"(ssr)/./node_modules/@mantine/core/esm/components/ModalBase/ModalBaseTitle.mjs","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@mantine/core/esm/components/ModalBase/NativeScrollArea.mjs":{"*":{"id":"(ssr)/./node_modules/@mantine/core/esm/components/ModalBase/NativeScrollArea.mjs","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@mantine/core/esm/components/MultiSelect/MultiSelect.mjs":{"*":{"id":"(ssr)/./node_modules/@mantine/core/esm/components/MultiSelect/MultiSelect.mjs","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@mantine/core/esm/components/NativeSelect/NativeSelect.mjs":{"*":{"id":"(ssr)/./node_modules/@mantine/core/esm/components/NativeSelect/NativeSelect.mjs","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@mantine/core/esm/components/NavLink/NavLink.mjs":{"*":{"id":"(ssr)/./node_modules/@mantine/core/esm/components/NavLink/NavLink.mjs","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@mantine/core/esm/components/Notification/Notification.mjs":{"*":{"id":"(ssr)/./node_modules/@mantine/core/esm/components/Notification/Notification.mjs","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@mantine/core/esm/components/NumberFormatter/NumberFormatter.mjs":{"*":{"id":"(ssr)/./node_modules/@mantine/core/esm/components/NumberFormatter/NumberFormatter.mjs","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@mantine/core/esm/components/NumberInput/NumberInput.mjs":{"*":{"id":"(ssr)/./node_modules/@mantine/core/esm/components/NumberInput/NumberInput.mjs","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@mantine/core/esm/components/Overlay/Overlay.mjs":{"*":{"id":"(ssr)/./node_modules/@mantine/core/esm/components/Overlay/Overlay.mjs","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@mantine/core/esm/components/Pagination/Pagination.mjs":{"*":{"id":"(ssr)/./node_modules/@mantine/core/esm/components/Pagination/Pagination.mjs","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@mantine/core/esm/components/Pagination/PaginationControl/PaginationControl.mjs":{"*":{"id":"(ssr)/./node_modules/@mantine/core/esm/components/Pagination/PaginationControl/PaginationControl.mjs","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@mantine/core/esm/components/Pagination/PaginationDots/PaginationDots.mjs":{"*":{"id":"(ssr)/./node_modules/@mantine/core/esm/components/Pagination/PaginationDots/PaginationDots.mjs","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@mantine/core/esm/components/Pagination/PaginationEdges/PaginationEdges.mjs":{"*":{"id":"(ssr)/./node_modules/@mantine/core/esm/components/Pagination/PaginationEdges/PaginationEdges.mjs","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@mantine/core/esm/components/Pagination/PaginationItems/PaginationItems.mjs":{"*":{"id":"(ssr)/./node_modules/@mantine/core/esm/components/Pagination/PaginationItems/PaginationItems.mjs","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@mantine/core/esm/components/Pagination/PaginationRoot/PaginationRoot.mjs":{"*":{"id":"(ssr)/./node_modules/@mantine/core/esm/components/Pagination/PaginationRoot/PaginationRoot.mjs","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@mantine/core/esm/components/Paper/Paper.mjs":{"*":{"id":"(ssr)/./node_modules/@mantine/core/esm/components/Paper/Paper.mjs","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@mantine/core/esm/components/PasswordInput/PasswordInput.mjs":{"*":{"id":"(ssr)/./node_modules/@mantine/core/esm/components/PasswordInput/PasswordInput.mjs","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@mantine/core/esm/components/Pill/Pill.mjs":{"*":{"id":"(ssr)/./node_modules/@mantine/core/esm/components/Pill/Pill.mjs","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@mantine/core/esm/components/Pill/PillGroup/PillGroup.mjs":{"*":{"id":"(ssr)/./node_modules/@mantine/core/esm/components/Pill/PillGroup/PillGroup.mjs","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@mantine/core/esm/components/PillsInput/PillsInput.mjs":{"*":{"id":"(ssr)/./node_modules/@mantine/core/esm/components/PillsInput/PillsInput.mjs","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@mantine/core/esm/components/PillsInput/PillsInputField/PillsInputField.mjs":{"*":{"id":"(ssr)/./node_modules/@mantine/core/esm/components/PillsInput/PillsInputField/PillsInputField.mjs","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@mantine/core/esm/components/PinInput/PinInput.mjs":{"*":{"id":"(ssr)/./node_modules/@mantine/core/esm/components/PinInput/PinInput.mjs","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@mantine/core/esm/components/Popover/Popover.mjs":{"*":{"id":"(ssr)/./node_modules/@mantine/core/esm/components/Popover/Popover.mjs","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@mantine/core/esm/components/Popover/PopoverDropdown/PopoverDropdown.mjs":{"*":{"id":"(ssr)/./node_modules/@mantine/core/esm/components/Popover/PopoverDropdown/PopoverDropdown.mjs","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@mantine/core/esm/components/Popover/PopoverTarget/PopoverTarget.mjs":{"*":{"id":"(ssr)/./node_modules/@mantine/core/esm/components/Popover/PopoverTarget/PopoverTarget.mjs","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@mantine/core/esm/components/Portal/OptionalPortal.mjs":{"*":{"id":"(ssr)/./node_modules/@mantine/core/esm/components/Portal/OptionalPortal.mjs","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@mantine/core/esm/components/Portal/Portal.mjs":{"*":{"id":"(ssr)/./node_modules/@mantine/core/esm/components/Portal/Portal.mjs","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@mantine/core/esm/components/Progress/Progress.mjs":{"*":{"id":"(ssr)/./node_modules/@mantine/core/esm/components/Progress/Progress.mjs","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@mantine/core/esm/components/Progress/ProgressLabel/ProgressLabel.mjs":{"*":{"id":"(ssr)/./node_modules/@mantine/core/esm/components/Progress/ProgressLabel/ProgressLabel.mjs","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@mantine/core/esm/components/Progress/ProgressRoot/ProgressRoot.mjs":{"*":{"id":"(ssr)/./node_modules/@mantine/core/esm/components/Progress/ProgressRoot/ProgressRoot.mjs","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@mantine/core/esm/components/Progress/ProgressSection/ProgressSection.mjs":{"*":{"id":"(ssr)/./node_modules/@mantine/core/esm/components/Progress/ProgressSection/ProgressSection.mjs","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@mantine/core/esm/components/Radio/Radio.mjs":{"*":{"id":"(ssr)/./node_modules/@mantine/core/esm/components/Radio/Radio.mjs","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@mantine/core/esm/components/Radio/RadioCard/RadioCard.context.mjs":{"*":{"id":"(ssr)/./node_modules/@mantine/core/esm/components/Radio/RadioCard/RadioCard.context.mjs","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@mantine/core/esm/components/Radio/RadioCard/RadioCard.mjs":{"*":{"id":"(ssr)/./node_modules/@mantine/core/esm/components/Radio/RadioCard/RadioCard.mjs","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@mantine/core/esm/components/Radio/RadioGroup/RadioGroup.mjs":{"*":{"id":"(ssr)/./node_modules/@mantine/core/esm/components/Radio/RadioGroup/RadioGroup.mjs","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@mantine/core/esm/components/Radio/RadioIcon.mjs":{"*":{"id":"(ssr)/./node_modules/@mantine/core/esm/components/Radio/RadioIcon.mjs","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@mantine/core/esm/components/Radio/RadioIndicator/RadioIndicator.mjs":{"*":{"id":"(ssr)/./node_modules/@mantine/core/esm/components/Radio/RadioIndicator/RadioIndicator.mjs","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@mantine/core/esm/components/Rating/Rating.mjs":{"*":{"id":"(ssr)/./node_modules/@mantine/core/esm/components/Rating/Rating.mjs","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@mantine/core/esm/components/RingProgress/RingProgress.mjs":{"*":{"id":"(ssr)/./node_modules/@mantine/core/esm/components/RingProgress/RingProgress.mjs","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@mantine/core/esm/components/ScrollArea/ScrollArea.mjs":{"*":{"id":"(ssr)/./node_modules/@mantine/core/esm/components/ScrollArea/ScrollArea.mjs","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@mantine/core/esm/components/SegmentedControl/SegmentedControl.mjs":{"*":{"id":"(ssr)/./node_modules/@mantine/core/esm/components/SegmentedControl/SegmentedControl.mjs","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@mantine/core/esm/components/Select/Select.mjs":{"*":{"id":"(ssr)/./node_modules/@mantine/core/esm/components/Select/Select.mjs","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@mantine/core/esm/components/SemiCircleProgress/SemiCircleProgress.mjs":{"*":{"id":"(ssr)/./node_modules/@mantine/core/esm/components/SemiCircleProgress/SemiCircleProgress.mjs","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@mantine/core/esm/components/SimpleGrid/SimpleGrid.mjs":{"*":{"id":"(ssr)/./node_modules/@mantine/core/esm/components/SimpleGrid/SimpleGrid.mjs","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@mantine/core/esm/components/Skeleton/Skeleton.mjs":{"*":{"id":"(ssr)/./node_modules/@mantine/core/esm/components/Skeleton/Skeleton.mjs","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@mantine/core/esm/components/Slider/RangeSlider/RangeSlider.mjs":{"*":{"id":"(ssr)/./node_modules/@mantine/core/esm/components/Slider/RangeSlider/RangeSlider.mjs","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@mantine/core/esm/components/Slider/Slider/Slider.mjs":{"*":{"id":"(ssr)/./node_modules/@mantine/core/esm/components/Slider/Slider/Slider.mjs","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@mantine/core/esm/components/Space/Space.mjs":{"*":{"id":"(ssr)/./node_modules/@mantine/core/esm/components/Space/Space.mjs","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@mantine/core/esm/components/Spoiler/Spoiler.mjs":{"*":{"id":"(ssr)/./node_modules/@mantine/core/esm/components/Spoiler/Spoiler.mjs","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@mantine/core/esm/components/Stack/Stack.mjs":{"*":{"id":"(ssr)/./node_modules/@mantine/core/esm/components/Stack/Stack.mjs","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@mantine/core/esm/components/Stepper/Stepper.mjs":{"*":{"id":"(ssr)/./node_modules/@mantine/core/esm/components/Stepper/Stepper.mjs","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@mantine/core/esm/components/Stepper/StepperCompleted/StepperCompleted.mjs":{"*":{"id":"(ssr)/./node_modules/@mantine/core/esm/components/Stepper/StepperCompleted/StepperCompleted.mjs","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@mantine/core/esm/components/Stepper/StepperStep/StepperStep.mjs":{"*":{"id":"(ssr)/./node_modules/@mantine/core/esm/components/Stepper/StepperStep/StepperStep.mjs","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@mantine/core/esm/components/Switch/Switch.mjs":{"*":{"id":"(ssr)/./node_modules/@mantine/core/esm/components/Switch/Switch.mjs","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@mantine/core/esm/components/Switch/SwitchGroup/SwitchGroup.mjs":{"*":{"id":"(ssr)/./node_modules/@mantine/core/esm/components/Switch/SwitchGroup/SwitchGroup.mjs","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@mantine/core/esm/components/Table/Table.components.mjs":{"*":{"id":"(ssr)/./node_modules/@mantine/core/esm/components/Table/Table.components.mjs","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@mantine/core/esm/components/Table/Table.mjs":{"*":{"id":"(ssr)/./node_modules/@mantine/core/esm/components/Table/Table.mjs","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@mantine/core/esm/components/Table/TableScrollContainer.mjs":{"*":{"id":"(ssr)/./node_modules/@mantine/core/esm/components/Table/TableScrollContainer.mjs","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@mantine/core/esm/components/TableOfContents/TableOfContents.mjs":{"*":{"id":"(ssr)/./node_modules/@mantine/core/esm/components/TableOfContents/TableOfContents.mjs","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@mantine/core/esm/components/Tabs/Tabs.mjs":{"*":{"id":"(ssr)/./node_modules/@mantine/core/esm/components/Tabs/Tabs.mjs","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@mantine/core/esm/components/Tabs/TabsList/TabsList.mjs":{"*":{"id":"(ssr)/./node_modules/@mantine/core/esm/components/Tabs/TabsList/TabsList.mjs","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@mantine/core/esm/components/Tabs/TabsPanel/TabsPanel.mjs":{"*":{"id":"(ssr)/./node_modules/@mantine/core/esm/components/Tabs/TabsPanel/TabsPanel.mjs","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@mantine/core/esm/components/Tabs/TabsTab/TabsTab.mjs":{"*":{"id":"(ssr)/./node_modules/@mantine/core/esm/components/Tabs/TabsTab/TabsTab.mjs","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@mantine/core/esm/components/TagsInput/TagsInput.mjs":{"*":{"id":"(ssr)/./node_modules/@mantine/core/esm/components/TagsInput/TagsInput.mjs","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@mantine/core/esm/components/Text/Text.mjs":{"*":{"id":"(ssr)/./node_modules/@mantine/core/esm/components/Text/Text.mjs","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@mantine/core/esm/components/Textarea/Textarea.mjs":{"*":{"id":"(ssr)/./node_modules/@mantine/core/esm/components/Textarea/Textarea.mjs","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@mantine/core/esm/components/TextInput/TextInput.mjs":{"*":{"id":"(ssr)/./node_modules/@mantine/core/esm/components/TextInput/TextInput.mjs","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@mantine/core/esm/components/ThemeIcon/ThemeIcon.mjs":{"*":{"id":"(ssr)/./node_modules/@mantine/core/esm/components/ThemeIcon/ThemeIcon.mjs","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@mantine/core/esm/components/Timeline/Timeline.mjs":{"*":{"id":"(ssr)/./node_modules/@mantine/core/esm/components/Timeline/Timeline.mjs","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@mantine/core/esm/components/Timeline/TimelineItem/TimelineItem.mjs":{"*":{"id":"(ssr)/./node_modules/@mantine/core/esm/components/Timeline/TimelineItem/TimelineItem.mjs","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@mantine/core/esm/components/Title/Title.mjs":{"*":{"id":"(ssr)/./node_modules/@mantine/core/esm/components/Title/Title.mjs","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@mantine/core/esm/components/Tooltip/Tooltip.mjs":{"*":{"id":"(ssr)/./node_modules/@mantine/core/esm/components/Tooltip/Tooltip.mjs","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@mantine/core/esm/components/Tooltip/TooltipFloating/TooltipFloating.mjs":{"*":{"id":"(ssr)/./node_modules/@mantine/core/esm/components/Tooltip/TooltipFloating/TooltipFloating.mjs","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@mantine/core/esm/components/Tooltip/TooltipGroup/TooltipGroup.mjs":{"*":{"id":"(ssr)/./node_modules/@mantine/core/esm/components/Tooltip/TooltipGroup/TooltipGroup.mjs","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@mantine/core/esm/components/Transition/get-transition-props/get-transition-props.mjs":{"*":{"id":"(ssr)/./node_modules/@mantine/core/esm/components/Transition/get-transition-props/get-transition-props.mjs","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@mantine/core/esm/components/Transition/Transition.mjs":{"*":{"id":"(ssr)/./node_modules/@mantine/core/esm/components/Transition/Transition.mjs","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@mantine/core/esm/components/Transition/transitions.mjs":{"*":{"id":"(ssr)/./node_modules/@mantine/core/esm/components/Transition/transitions.mjs","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@mantine/core/esm/components/Tree/Tree.mjs":{"*":{"id":"(ssr)/./node_modules/@mantine/core/esm/components/Tree/Tree.mjs","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@mantine/core/esm/components/Tree/use-tree.mjs":{"*":{"id":"(ssr)/./node_modules/@mantine/core/esm/components/Tree/use-tree.mjs","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@mantine/core/esm/components/TypographyStylesProvider/TypographyStylesProvider.mjs":{"*":{"id":"(ssr)/./node_modules/@mantine/core/esm/components/TypographyStylesProvider/TypographyStylesProvider.mjs","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@mantine/core/esm/components/UnstyledButton/UnstyledButton.mjs":{"*":{"id":"(ssr)/./node_modules/@mantine/core/esm/components/UnstyledButton/UnstyledButton.mjs","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@mantine/core/esm/components/VisuallyHidden/VisuallyHidden.mjs":{"*":{"id":"(ssr)/./node_modules/@mantine/core/esm/components/VisuallyHidden/VisuallyHidden.mjs","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@mantine/core/esm/core/Box/Box.mjs":{"*":{"id":"(ssr)/./node_modules/@mantine/core/esm/core/Box/Box.mjs","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@mantine/core/esm/core/Box/get-style-object/get-style-object.mjs":{"*":{"id":"(ssr)/./node_modules/@mantine/core/esm/core/Box/get-style-object/get-style-object.mjs","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@mantine/core/esm/core/Box/style-props/extract-style-props/extract-style-props.mjs":{"*":{"id":"(ssr)/./node_modules/@mantine/core/esm/core/Box/style-props/extract-style-props/extract-style-props.mjs","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@mantine/core/esm/core/Box/style-props/parse-style-props/parse-style-props.mjs":{"*":{"id":"(ssr)/./node_modules/@mantine/core/esm/core/Box/style-props/parse-style-props/parse-style-props.mjs","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@mantine/core/esm/core/Box/style-props/style-props-data.mjs":{"*":{"id":"(ssr)/./node_modules/@mantine/core/esm/core/Box/style-props/style-props-data.mjs","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@mantine/core/esm/core/Box/use-random-classname/use-random-classname.mjs":{"*":{"id":"(ssr)/./node_modules/@mantine/core/esm/core/Box/use-random-classname/use-random-classname.mjs","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@mantine/core/esm/core/DirectionProvider/DirectionProvider.mjs":{"*":{"id":"(ssr)/./node_modules/@mantine/core/esm/core/DirectionProvider/DirectionProvider.mjs","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@mantine/core/esm/core/factory/factory.mjs":{"*":{"id":"(ssr)/./node_modules/@mantine/core/esm/core/factory/factory.mjs","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@mantine/core/esm/core/factory/polymorphic-factory.mjs":{"*":{"id":"(ssr)/./node_modules/@mantine/core/esm/core/factory/polymorphic-factory.mjs","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@mantine/core/esm/core/InlineStyles/InlineStyles.mjs":{"*":{"id":"(ssr)/./node_modules/@mantine/core/esm/core/InlineStyles/InlineStyles.mjs","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@mantine/core/esm/core/InlineStyles/styles-to-string/styles-to-string.mjs":{"*":{"id":"(ssr)/./node_modules/@mantine/core/esm/core/InlineStyles/styles-to-string/styles-to-string.mjs","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@mantine/core/esm/core/MantineProvider/color-functions/default-variant-colors-resolver/default-variant-colors-resolver.mjs":{"*":{"id":"(ssr)/./node_modules/@mantine/core/esm/core/MantineProvider/color-functions/default-variant-colors-resolver/default-variant-colors-resolver.mjs","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@mantine/core/esm/core/MantineProvider/color-functions/get-auto-contrast-value/get-auto-contrast-value.mjs":{"*":{"id":"(ssr)/./node_modules/@mantine/core/esm/core/MantineProvider/color-functions/get-auto-contrast-value/get-auto-contrast-value.mjs","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@mantine/core/esm/core/MantineProvider/color-functions/get-contrast-color/get-contrast-color.mjs":{"*":{"id":"(ssr)/./node_modules/@mantine/core/esm/core/MantineProvider/color-functions/get-contrast-color/get-contrast-color.mjs","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@mantine/core/esm/core/MantineProvider/color-functions/get-gradient/get-gradient.mjs":{"*":{"id":"(ssr)/./node_modules/@mantine/core/esm/core/MantineProvider/color-functions/get-gradient/get-gradient.mjs","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@mantine/core/esm/core/MantineProvider/color-functions/get-primary-shade/get-primary-shade.mjs":{"*":{"id":"(ssr)/./node_modules/@mantine/core/esm/core/MantineProvider/color-functions/get-primary-shade/get-primary-shade.mjs","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@mantine/core/esm/core/MantineProvider/color-functions/get-theme-color/get-theme-color.mjs":{"*":{"id":"(ssr)/./node_modules/@mantine/core/esm/core/MantineProvider/color-functions/get-theme-color/get-theme-color.mjs","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@mantine/core/esm/core/MantineProvider/color-functions/parse-theme-color/parse-theme-color.mjs":{"*":{"id":"(ssr)/./node_modules/@mantine/core/esm/core/MantineProvider/color-functions/parse-theme-color/parse-theme-color.mjs","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@mantine/core/esm/core/MantineProvider/color-scheme-managers/is-mantine-color-scheme.mjs":{"*":{"id":"(ssr)/./node_modules/@mantine/core/esm/core/MantineProvider/color-scheme-managers/is-mantine-color-scheme.mjs","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@mantine/core/esm/core/MantineProvider/color-scheme-managers/local-storage-manager.mjs":{"*":{"id":"(ssr)/./node_modules/@mantine/core/esm/core/MantineProvider/color-scheme-managers/local-storage-manager.mjs","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@mantine/core/esm/core/MantineProvider/ColorSchemeScript/ColorSchemeScript.mjs":{"*":{"id":"(ssr)/./node_modules/@mantine/core/esm/core/MantineProvider/ColorSchemeScript/ColorSchemeScript.mjs","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@mantine/core/esm/core/MantineProvider/convert-css-variables/convert-css-variables.mjs":{"*":{"id":"(ssr)/./node_modules/@mantine/core/esm/core/MantineProvider/convert-css-variables/convert-css-variables.mjs","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@mantine/core/esm/core/MantineProvider/Mantine.context.mjs":{"*":{"id":"(ssr)/./node_modules/@mantine/core/esm/core/MantineProvider/Mantine.context.mjs","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@mantine/core/esm/core/MantineProvider/MantineCssVariables/default-css-variables-resolver.mjs":{"*":{"id":"(ssr)/./node_modules/@mantine/core/esm/core/MantineProvider/MantineCssVariables/default-css-variables-resolver.mjs","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@mantine/core/esm/core/MantineProvider/MantineCssVariables/get-css-color-variables.mjs":{"*":{"id":"(ssr)/./node_modules/@mantine/core/esm/core/MantineProvider/MantineCssVariables/get-css-color-variables.mjs","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@mantine/core/esm/core/MantineProvider/MantineCssVariables/MantineCssVariables.mjs":{"*":{"id":"(ssr)/./node_modules/@mantine/core/esm/core/MantineProvider/MantineCssVariables/MantineCssVariables.mjs","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@mantine/core/esm/core/MantineProvider/MantineProvider.mjs":{"*":{"id":"(ssr)/./node_modules/@mantine/core/esm/core/MantineProvider/MantineProvider.mjs","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@mantine/core/esm/core/MantineProvider/MantineThemeProvider/MantineThemeProvider.mjs":{"*":{"id":"(ssr)/./node_modules/@mantine/core/esm/core/MantineProvider/MantineThemeProvider/MantineThemeProvider.mjs","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@mantine/core/esm/core/MantineProvider/merge-theme-overrides/merge-theme-overrides.mjs":{"*":{"id":"(ssr)/./node_modules/@mantine/core/esm/core/MantineProvider/merge-theme-overrides/merge-theme-overrides.mjs","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@mantine/core/esm/core/MantineProvider/use-mantine-color-scheme/use-computed-color-scheme.mjs":{"*":{"id":"(ssr)/./node_modules/@mantine/core/esm/core/MantineProvider/use-mantine-color-scheme/use-computed-color-scheme.mjs","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@mantine/core/esm/core/MantineProvider/use-mantine-color-scheme/use-mantine-color-scheme.mjs":{"*":{"id":"(ssr)/./node_modules/@mantine/core/esm/core/MantineProvider/use-mantine-color-scheme/use-mantine-color-scheme.mjs","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@mantine/core/esm/core/MantineProvider/use-mantine-color-scheme/use-provider-color-scheme.mjs":{"*":{"id":"(ssr)/./node_modules/@mantine/core/esm/core/MantineProvider/use-mantine-color-scheme/use-provider-color-scheme.mjs","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@mantine/core/esm/core/MantineProvider/use-matches/use-matches.mjs":{"*":{"id":"(ssr)/./node_modules/@mantine/core/esm/core/MantineProvider/use-matches/use-matches.mjs","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@mantine/core/esm/core/MantineProvider/use-props/use-props.mjs":{"*":{"id":"(ssr)/./node_modules/@mantine/core/esm/core/MantineProvider/use-props/use-props.mjs","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@mantine/core/esm/core/styles-api/create-vars-resolver/create-vars-resolver.mjs":{"*":{"id":"(ssr)/./node_modules/@mantine/core/esm/core/styles-api/create-vars-resolver/create-vars-resolver.mjs","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@mantine/core/esm/core/styles-api/use-resolved-styles-api/use-resolved-styles-api.mjs":{"*":{"id":"(ssr)/./node_modules/@mantine/core/esm/core/styles-api/use-resolved-styles-api/use-resolved-styles-api.mjs","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@mantine/core/esm/core/styles-api/use-styles/get-class-name/get-global-class-names/get-global-class-names.mjs":{"*":{"id":"(ssr)/./node_modules/@mantine/core/esm/core/styles-api/use-styles/get-class-name/get-global-class-names/get-global-class-names.mjs","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@mantine/core/esm/core/styles-api/use-styles/get-class-name/resolve-class-names/resolve-class-names.mjs":{"*":{"id":"(ssr)/./node_modules/@mantine/core/esm/core/styles-api/use-styles/get-class-name/resolve-class-names/resolve-class-names.mjs","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@mantine/core/esm/core/styles-api/use-styles/get-style/resolve-styles/resolve-styles.mjs":{"*":{"id":"(ssr)/./node_modules/@mantine/core/esm/core/styles-api/use-styles/get-style/resolve-styles/resolve-styles.mjs","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@mantine/core/esm/core/styles-api/use-styles/use-styles.mjs":{"*":{"id":"(ssr)/./node_modules/@mantine/core/esm/core/styles-api/use-styles/use-styles.mjs","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@mantine/core/esm/core/utils/camel-to-kebab-case/camel-to-kebab-case.mjs":{"*":{"id":"(ssr)/./node_modules/@mantine/core/esm/core/utils/camel-to-kebab-case/camel-to-kebab-case.mjs","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@mantine/core/esm/core/utils/close-on-escape/close-on-escape.mjs":{"*":{"id":"(ssr)/./node_modules/@mantine/core/esm/core/utils/close-on-escape/close-on-escape.mjs","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@mantine/core/esm/core/utils/create-event-handler/create-event-handler.mjs":{"*":{"id":"(ssr)/./node_modules/@mantine/core/esm/core/utils/create-event-handler/create-event-handler.mjs","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@mantine/core/esm/core/utils/create-optional-context/create-optional-context.mjs":{"*":{"id":"(ssr)/./node_modules/@mantine/core/esm/core/utils/create-optional-context/create-optional-context.mjs","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@mantine/core/esm/core/utils/create-safe-context/create-safe-context.mjs":{"*":{"id":"(ssr)/./node_modules/@mantine/core/esm/core/utils/create-safe-context/create-safe-context.mjs","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@mantine/core/esm/core/utils/create-scoped-keydown-handler/create-scoped-keydown-handler.mjs":{"*":{"id":"(ssr)/./node_modules/@mantine/core/esm/core/utils/create-scoped-keydown-handler/create-scoped-keydown-handler.mjs","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@mantine/core/esm/core/utils/create-use-external-events/create-use-external-events.mjs":{"*":{"id":"(ssr)/./node_modules/@mantine/core/esm/core/utils/create-use-external-events/create-use-external-events.mjs","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@mantine/core/esm/core/utils/filter-props/filter-props.mjs":{"*":{"id":"(ssr)/./node_modules/@mantine/core/esm/core/utils/filter-props/filter-props.mjs","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@mantine/core/esm/core/utils/find-closest-number/find-closest-number.mjs":{"*":{"id":"(ssr)/./node_modules/@mantine/core/esm/core/utils/find-closest-number/find-closest-number.mjs","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@mantine/core/esm/core/utils/find-element-ancestor/find-element-ancestor.mjs":{"*":{"id":"(ssr)/./node_modules/@mantine/core/esm/core/utils/find-element-ancestor/find-element-ancestor.mjs","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@mantine/core/esm/core/utils/get-base-value/get-base-value.mjs":{"*":{"id":"(ssr)/./node_modules/@mantine/core/esm/core/utils/get-base-value/get-base-value.mjs","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@mantine/core/esm/core/utils/get-breakpoint-value/get-breakpoint-value.mjs":{"*":{"id":"(ssr)/./node_modules/@mantine/core/esm/core/utils/get-breakpoint-value/get-breakpoint-value.mjs","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@mantine/core/esm/core/utils/get-context-item-index/get-context-item-index.mjs":{"*":{"id":"(ssr)/./node_modules/@mantine/core/esm/core/utils/get-context-item-index/get-context-item-index.mjs","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@mantine/core/esm/core/utils/get-default-z-index/get-default-z-index.mjs":{"*":{"id":"(ssr)/./node_modules/@mantine/core/esm/core/utils/get-default-z-index/get-default-z-index.mjs","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@mantine/core/esm/core/utils/get-env/get-env.mjs":{"*":{"id":"(ssr)/./node_modules/@mantine/core/esm/core/utils/get-env/get-env.mjs","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@mantine/core/esm/core/utils/get-ref-prop/get-ref-prop.mjs":{"*":{"id":"(ssr)/./node_modules/@mantine/core/esm/core/utils/get-ref-prop/get-ref-prop.mjs","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@mantine/core/esm/core/utils/get-safe-id/get-safe-id.mjs":{"*":{"id":"(ssr)/./node_modules/@mantine/core/esm/core/utils/get-safe-id/get-safe-id.mjs","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@mantine/core/esm/core/utils/get-size/get-size.mjs":{"*":{"id":"(ssr)/./node_modules/@mantine/core/esm/core/utils/get-size/get-size.mjs","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@mantine/core/esm/core/utils/get-sorted-breakpoints/get-sorted-breakpoints.mjs":{"*":{"id":"(ssr)/./node_modules/@mantine/core/esm/core/utils/get-sorted-breakpoints/get-sorted-breakpoints.mjs","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@mantine/core/esm/core/utils/is-element/is-element.mjs":{"*":{"id":"(ssr)/./node_modules/@mantine/core/esm/core/utils/is-element/is-element.mjs","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@mantine/core/esm/core/utils/is-number-like/is-number-like.mjs":{"*":{"id":"(ssr)/./node_modules/@mantine/core/esm/core/utils/is-number-like/is-number-like.mjs","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@mantine/core/esm/core/utils/keys/keys.mjs":{"*":{"id":"(ssr)/./node_modules/@mantine/core/esm/core/utils/keys/keys.mjs","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@mantine/core/esm/core/utils/memoize/memoize.mjs":{"*":{"id":"(ssr)/./node_modules/@mantine/core/esm/core/utils/memoize/memoize.mjs","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@mantine/core/esm/core/utils/noop/noop.mjs":{"*":{"id":"(ssr)/./node_modules/@mantine/core/esm/core/utils/noop/noop.mjs","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@mantine/core/esm/core/utils/use-hovered/use-hovered.mjs":{"*":{"id":"(ssr)/./node_modules/@mantine/core/esm/core/utils/use-hovered/use-hovered.mjs","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@mantine/hooks/esm/use-callback-ref/use-callback-ref.mjs":{"*":{"id":"(ssr)/./node_modules/@mantine/hooks/esm/use-callback-ref/use-callback-ref.mjs","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@mantine/hooks/esm/use-click-outside/use-click-outside.mjs":{"*":{"id":"(ssr)/./node_modules/@mantine/hooks/esm/use-click-outside/use-click-outside.mjs","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@mantine/hooks/esm/use-clipboard/use-clipboard.mjs":{"*":{"id":"(ssr)/./node_modules/@mantine/hooks/esm/use-clipboard/use-clipboard.mjs","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@mantine/hooks/esm/use-color-scheme/use-color-scheme.mjs":{"*":{"id":"(ssr)/./node_modules/@mantine/hooks/esm/use-color-scheme/use-color-scheme.mjs","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@mantine/hooks/esm/use-counter/use-counter.mjs":{"*":{"id":"(ssr)/./node_modules/@mantine/hooks/esm/use-counter/use-counter.mjs","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@mantine/hooks/esm/use-debounced-callback/use-debounced-callback.mjs":{"*":{"id":"(ssr)/./node_modules/@mantine/hooks/esm/use-debounced-callback/use-debounced-callback.mjs","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@mantine/hooks/esm/use-debounced-state/use-debounced-state.mjs":{"*":{"id":"(ssr)/./node_modules/@mantine/hooks/esm/use-debounced-state/use-debounced-state.mjs","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@mantine/hooks/esm/use-debounced-value/use-debounced-value.mjs":{"*":{"id":"(ssr)/./node_modules/@mantine/hooks/esm/use-debounced-value/use-debounced-value.mjs","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@mantine/hooks/esm/use-did-update/use-did-update.mjs":{"*":{"id":"(ssr)/./node_modules/@mantine/hooks/esm/use-did-update/use-did-update.mjs","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@mantine/hooks/esm/use-disclosure/use-disclosure.mjs":{"*":{"id":"(ssr)/./node_modules/@mantine/hooks/esm/use-disclosure/use-disclosure.mjs","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@mantine/hooks/esm/use-document-title/use-document-title.mjs":{"*":{"id":"(ssr)/./node_modules/@mantine/hooks/esm/use-document-title/use-document-title.mjs","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@mantine/hooks/esm/use-document-visibility/use-document-visibility.mjs":{"*":{"id":"(ssr)/./node_modules/@mantine/hooks/esm/use-document-visibility/use-document-visibility.mjs","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@mantine/hooks/esm/use-event-listener/use-event-listener.mjs":{"*":{"id":"(ssr)/./node_modules/@mantine/hooks/esm/use-event-listener/use-event-listener.mjs","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@mantine/hooks/esm/use-eye-dropper/use-eye-dropper.mjs":{"*":{"id":"(ssr)/./node_modules/@mantine/hooks/esm/use-eye-dropper/use-eye-dropper.mjs","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@mantine/hooks/esm/use-favicon/use-favicon.mjs":{"*":{"id":"(ssr)/./node_modules/@mantine/hooks/esm/use-favicon/use-favicon.mjs","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@mantine/hooks/esm/use-fetch/use-fetch.mjs":{"*":{"id":"(ssr)/./node_modules/@mantine/hooks/esm/use-fetch/use-fetch.mjs","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@mantine/hooks/esm/use-file-dialog/use-file-dialog.mjs":{"*":{"id":"(ssr)/./node_modules/@mantine/hooks/esm/use-file-dialog/use-file-dialog.mjs","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@mantine/hooks/esm/use-focus-return/use-focus-return.mjs":{"*":{"id":"(ssr)/./node_modules/@mantine/hooks/esm/use-focus-return/use-focus-return.mjs","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@mantine/hooks/esm/use-focus-trap/use-focus-trap.mjs":{"*":{"id":"(ssr)/./node_modules/@mantine/hooks/esm/use-focus-trap/use-focus-trap.mjs","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@mantine/hooks/esm/use-focus-within/use-focus-within.mjs":{"*":{"id":"(ssr)/./node_modules/@mantine/hooks/esm/use-focus-within/use-focus-within.mjs","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@mantine/hooks/esm/use-force-update/use-force-update.mjs":{"*":{"id":"(ssr)/./node_modules/@mantine/hooks/esm/use-force-update/use-force-update.mjs","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@mantine/hooks/esm/use-fullscreen/use-fullscreen.mjs":{"*":{"id":"(ssr)/./node_modules/@mantine/hooks/esm/use-fullscreen/use-fullscreen.mjs","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@mantine/hooks/esm/use-hash/use-hash.mjs":{"*":{"id":"(ssr)/./node_modules/@mantine/hooks/esm/use-hash/use-hash.mjs","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@mantine/hooks/esm/use-headroom/use-headroom.mjs":{"*":{"id":"(ssr)/./node_modules/@mantine/hooks/esm/use-headroom/use-headroom.mjs","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@mantine/hooks/esm/use-hotkeys/parse-hotkey.mjs":{"*":{"id":"(ssr)/./node_modules/@mantine/hooks/esm/use-hotkeys/parse-hotkey.mjs","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@mantine/hooks/esm/use-hotkeys/use-hotkeys.mjs":{"*":{"id":"(ssr)/./node_modules/@mantine/hooks/esm/use-hotkeys/use-hotkeys.mjs","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@mantine/hooks/esm/use-hover/use-hover.mjs":{"*":{"id":"(ssr)/./node_modules/@mantine/hooks/esm/use-hover/use-hover.mjs","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@mantine/hooks/esm/use-id/use-id.mjs":{"*":{"id":"(ssr)/./node_modules/@mantine/hooks/esm/use-id/use-id.mjs","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@mantine/hooks/esm/use-idle/use-idle.mjs":{"*":{"id":"(ssr)/./node_modules/@mantine/hooks/esm/use-idle/use-idle.mjs","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@mantine/hooks/esm/use-in-viewport/use-in-viewport.mjs":{"*":{"id":"(ssr)/./node_modules/@mantine/hooks/esm/use-in-viewport/use-in-viewport.mjs","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@mantine/hooks/esm/use-input-state/use-input-state.mjs":{"*":{"id":"(ssr)/./node_modules/@mantine/hooks/esm/use-input-state/use-input-state.mjs","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@mantine/hooks/esm/use-intersection/use-intersection.mjs":{"*":{"id":"(ssr)/./node_modules/@mantine/hooks/esm/use-intersection/use-intersection.mjs","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@mantine/hooks/esm/use-interval/use-interval.mjs":{"*":{"id":"(ssr)/./node_modules/@mantine/hooks/esm/use-interval/use-interval.mjs","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@mantine/hooks/esm/use-is-first-render/use-is-first-render.mjs":{"*":{"id":"(ssr)/./node_modules/@mantine/hooks/esm/use-is-first-render/use-is-first-render.mjs","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@mantine/hooks/esm/use-isomorphic-effect/use-isomorphic-effect.mjs":{"*":{"id":"(ssr)/./node_modules/@mantine/hooks/esm/use-isomorphic-effect/use-isomorphic-effect.mjs","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@mantine/hooks/esm/use-list-state/use-list-state.mjs":{"*":{"id":"(ssr)/./node_modules/@mantine/hooks/esm/use-list-state/use-list-state.mjs","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@mantine/hooks/esm/use-local-storage/use-local-storage.mjs":{"*":{"id":"(ssr)/./node_modules/@mantine/hooks/esm/use-local-storage/use-local-storage.mjs","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@mantine/hooks/esm/use-logger/use-logger.mjs":{"*":{"id":"(ssr)/./node_modules/@mantine/hooks/esm/use-logger/use-logger.mjs","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@mantine/hooks/esm/use-map/use-map.mjs":{"*":{"id":"(ssr)/./node_modules/@mantine/hooks/esm/use-map/use-map.mjs","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@mantine/hooks/esm/use-media-query/use-media-query.mjs":{"*":{"id":"(ssr)/./node_modules/@mantine/hooks/esm/use-media-query/use-media-query.mjs","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@mantine/hooks/esm/use-merged-ref/use-merged-ref.mjs":{"*":{"id":"(ssr)/./node_modules/@mantine/hooks/esm/use-merged-ref/use-merged-ref.mjs","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@mantine/hooks/esm/use-mounted/use-mounted.mjs":{"*":{"id":"(ssr)/./node_modules/@mantine/hooks/esm/use-mounted/use-mounted.mjs","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@mantine/hooks/esm/use-mouse/use-mouse.mjs":{"*":{"id":"(ssr)/./node_modules/@mantine/hooks/esm/use-mouse/use-mouse.mjs","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@mantine/hooks/esm/use-move/use-move.mjs":{"*":{"id":"(ssr)/./node_modules/@mantine/hooks/esm/use-move/use-move.mjs","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@mantine/hooks/esm/use-mutation-observer/use-mutation-observer.mjs":{"*":{"id":"(ssr)/./node_modules/@mantine/hooks/esm/use-mutation-observer/use-mutation-observer.mjs","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@mantine/hooks/esm/use-network/use-network.mjs":{"*":{"id":"(ssr)/./node_modules/@mantine/hooks/esm/use-network/use-network.mjs","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@mantine/hooks/esm/use-orientation/use-orientation.mjs":{"*":{"id":"(ssr)/./node_modules/@mantine/hooks/esm/use-orientation/use-orientation.mjs","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@mantine/hooks/esm/use-os/use-os.mjs":{"*":{"id":"(ssr)/./node_modules/@mantine/hooks/esm/use-os/use-os.mjs","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@mantine/hooks/esm/use-page-leave/use-page-leave.mjs":{"*":{"id":"(ssr)/./node_modules/@mantine/hooks/esm/use-page-leave/use-page-leave.mjs","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@mantine/hooks/esm/use-pagination/use-pagination.mjs":{"*":{"id":"(ssr)/./node_modules/@mantine/hooks/esm/use-pagination/use-pagination.mjs","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@mantine/hooks/esm/use-previous/use-previous.mjs":{"*":{"id":"(ssr)/./node_modules/@mantine/hooks/esm/use-previous/use-previous.mjs","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@mantine/hooks/esm/use-queue/use-queue.mjs":{"*":{"id":"(ssr)/./node_modules/@mantine/hooks/esm/use-queue/use-queue.mjs","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@mantine/hooks/esm/use-radial-move/use-radial-move.mjs":{"*":{"id":"(ssr)/./node_modules/@mantine/hooks/esm/use-radial-move/use-radial-move.mjs","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@mantine/hooks/esm/use-reduced-motion/use-reduced-motion.mjs":{"*":{"id":"(ssr)/./node_modules/@mantine/hooks/esm/use-reduced-motion/use-reduced-motion.mjs","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@mantine/hooks/esm/use-resize-observer/use-resize-observer.mjs":{"*":{"id":"(ssr)/./node_modules/@mantine/hooks/esm/use-resize-observer/use-resize-observer.mjs","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@mantine/hooks/esm/use-scroll-into-view/use-scroll-into-view.mjs":{"*":{"id":"(ssr)/./node_modules/@mantine/hooks/esm/use-scroll-into-view/use-scroll-into-view.mjs","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@mantine/hooks/esm/use-scroll-spy/use-scroll-spy.mjs":{"*":{"id":"(ssr)/./node_modules/@mantine/hooks/esm/use-scroll-spy/use-scroll-spy.mjs","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@mantine/hooks/esm/use-session-storage/use-session-storage.mjs":{"*":{"id":"(ssr)/./node_modules/@mantine/hooks/esm/use-session-storage/use-session-storage.mjs","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@mantine/hooks/esm/use-set-state/use-set-state.mjs":{"*":{"id":"(ssr)/./node_modules/@mantine/hooks/esm/use-set-state/use-set-state.mjs","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@mantine/hooks/esm/use-set/use-set.mjs":{"*":{"id":"(ssr)/./node_modules/@mantine/hooks/esm/use-set/use-set.mjs","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@mantine/hooks/esm/use-shallow-effect/use-shallow-effect.mjs":{"*":{"id":"(ssr)/./node_modules/@mantine/hooks/esm/use-shallow-effect/use-shallow-effect.mjs","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@mantine/hooks/esm/use-state-history/use-state-history.mjs":{"*":{"id":"(ssr)/./node_modules/@mantine/hooks/esm/use-state-history/use-state-history.mjs","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@mantine/hooks/esm/use-text-selection/use-text-selection.mjs":{"*":{"id":"(ssr)/./node_modules/@mantine/hooks/esm/use-text-selection/use-text-selection.mjs","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@mantine/hooks/esm/use-throttled-callback/use-throttled-callback.mjs":{"*":{"id":"(ssr)/./node_modules/@mantine/hooks/esm/use-throttled-callback/use-throttled-callback.mjs","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@mantine/hooks/esm/use-throttled-state/use-throttled-state.mjs":{"*":{"id":"(ssr)/./node_modules/@mantine/hooks/esm/use-throttled-state/use-throttled-state.mjs","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@mantine/hooks/esm/use-throttled-value/use-throttled-value.mjs":{"*":{"id":"(ssr)/./node_modules/@mantine/hooks/esm/use-throttled-value/use-throttled-value.mjs","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@mantine/hooks/esm/use-timeout/use-timeout.mjs":{"*":{"id":"(ssr)/./node_modules/@mantine/hooks/esm/use-timeout/use-timeout.mjs","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@mantine/hooks/esm/use-toggle/use-toggle.mjs":{"*":{"id":"(ssr)/./node_modules/@mantine/hooks/esm/use-toggle/use-toggle.mjs","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@mantine/hooks/esm/use-uncontrolled/use-uncontrolled.mjs":{"*":{"id":"(ssr)/./node_modules/@mantine/hooks/esm/use-uncontrolled/use-uncontrolled.mjs","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@mantine/hooks/esm/use-validated-state/use-validated-state.mjs":{"*":{"id":"(ssr)/./node_modules/@mantine/hooks/esm/use-validated-state/use-validated-state.mjs","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@mantine/hooks/esm/use-viewport-size/use-viewport-size.mjs":{"*":{"id":"(ssr)/./node_modules/@mantine/hooks/esm/use-viewport-size/use-viewport-size.mjs","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@mantine/hooks/esm/use-window-event/use-window-event.mjs":{"*":{"id":"(ssr)/./node_modules/@mantine/hooks/esm/use-window-event/use-window-event.mjs","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@mantine/hooks/esm/use-window-scroll/use-window-scroll.mjs":{"*":{"id":"(ssr)/./node_modules/@mantine/hooks/esm/use-window-scroll/use-window-scroll.mjs","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@mantine/hooks/esm/utils/clamp/clamp.mjs":{"*":{"id":"(ssr)/./node_modules/@mantine/hooks/esm/utils/clamp/clamp.mjs","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@mantine/hooks/esm/utils/lower-first/lower-first.mjs":{"*":{"id":"(ssr)/./node_modules/@mantine/hooks/esm/utils/lower-first/lower-first.mjs","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@mantine/hooks/esm/utils/random-id/random-id.mjs":{"*":{"id":"(ssr)/./node_modules/@mantine/hooks/esm/utils/random-id/random-id.mjs","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@mantine/hooks/esm/utils/range/range.mjs":{"*":{"id":"(ssr)/./node_modules/@mantine/hooks/esm/utils/range/range.mjs","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@mantine/hooks/esm/utils/shallow-equal/shallow-equal.mjs":{"*":{"id":"(ssr)/./node_modules/@mantine/hooks/esm/utils/shallow-equal/shallow-equal.mjs","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@mantine/hooks/esm/utils/upper-first/upper-first.mjs":{"*":{"id":"(ssr)/./node_modules/@mantine/hooks/esm/utils/upper-first/upper-first.mjs","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@mantine/modals/esm/events.mjs":{"*":{"id":"(ssr)/./node_modules/@mantine/modals/esm/events.mjs","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@mantine/modals/esm/ModalsProvider.mjs":{"*":{"id":"(ssr)/./node_modules/@mantine/modals/esm/ModalsProvider.mjs","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@mantine/modals/esm/use-modals/use-modals.mjs":{"*":{"id":"(ssr)/./node_modules/@mantine/modals/esm/use-modals/use-modals.mjs","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@mantine/notifications/esm/Notifications.mjs":{"*":{"id":"(ssr)/./node_modules/@mantine/notifications/esm/Notifications.mjs","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@mantine/notifications/esm/notifications.store.mjs":{"*":{"id":"(ssr)/./node_modules/@mantine/notifications/esm/notifications.store.mjs","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/nextjs-toploader/dist/index.js":{"*":{"id":"(ssr)/./node_modules/nextjs-toploader/dist/index.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/simplebar-react/dist/simplebar.min.css":{"*":{"id":"(ssr)/./node_modules/simplebar-react/dist/simplebar.min.css","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/components/scrollToTop/ScrollToTop.tsx":{"*":{"id":"(ssr)/./src/components/scrollToTop/ScrollToTop.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/hooks/LocaleProvider.tsx":{"*":{"id":"(ssr)/./src/hooks/LocaleProvider.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/hooks/useAuth.tsx":{"*":{"id":"(ssr)/./src/hooks/useAuth.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/(dashboard)/CalendarLayoutClient.tsx":{"*":{"id":"(ssr)/./src/app/(dashboard)/CalendarLayoutClient.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/(dashboard)/web/page.tsx":{"*":{"id":"(ssr)/./src/app/(dashboard)/web/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/client-page.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/client-page.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/client-segment.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/client-segment.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/error-boundary.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/error-boundary.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/layout-router.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/layout-router.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/metadata/async-metadata.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/metadata/async-metadata.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/render-from-template-context.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/render-from-template-context.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/contexts/DayslotGetter.tsx":{"*":{"id":"(ssr)/./src/contexts/DayslotGetter.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/(dashboard)/appointments/page.tsx":{"*":{"id":"(ssr)/./src/app/(dashboard)/appointments/page.tsx","name":"*","chunks":[],"async":false}}},"edgeSSRModuleMapping":{},"clientModules":{"C:\\Users\\<USER>\\Desktop\\svp\\frontend\\medicine\\node_modules\\@mantine\\core\\esm\\components\\Accordion\\Accordion.mjs":{"id":"(app-pages-browser)/./node_modules/@mantine/core/esm/components/Accordion/Accordion.mjs","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\svp\\frontend\\medicine\\node_modules\\@mantine\\core\\esm\\components\\Accordion\\AccordionChevron.mjs":{"id":"(app-pages-browser)/./node_modules/@mantine/core/esm/components/Accordion/AccordionChevron.mjs","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\svp\\frontend\\medicine\\node_modules\\@mantine\\core\\esm\\components\\Accordion\\AccordionControl\\AccordionControl.mjs":{"id":"(app-pages-browser)/./node_modules/@mantine/core/esm/components/Accordion/AccordionControl/AccordionControl.mjs","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\svp\\frontend\\medicine\\node_modules\\@mantine\\core\\esm\\components\\Accordion\\AccordionItem\\AccordionItem.mjs":{"id":"(app-pages-browser)/./node_modules/@mantine/core/esm/components/Accordion/AccordionItem/AccordionItem.mjs","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\svp\\frontend\\medicine\\node_modules\\@mantine\\core\\esm\\components\\Accordion\\AccordionPanel\\AccordionPanel.mjs":{"id":"(app-pages-browser)/./node_modules/@mantine/core/esm/components/Accordion/AccordionPanel/AccordionPanel.mjs","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\svp\\frontend\\medicine\\node_modules\\@mantine\\core\\esm\\components\\ActionIcon\\ActionIcon.mjs":{"id":"(app-pages-browser)/./node_modules/@mantine/core/esm/components/ActionIcon/ActionIcon.mjs","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\svp\\frontend\\medicine\\node_modules\\@mantine\\core\\esm\\components\\ActionIcon\\ActionIconGroup\\ActionIconGroup.mjs":{"id":"(app-pages-browser)/./node_modules/@mantine/core/esm/components/ActionIcon/ActionIconGroup/ActionIconGroup.mjs","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\svp\\frontend\\medicine\\node_modules\\@mantine\\core\\esm\\components\\ActionIcon\\ActionIconGroupSection\\ActionIconGroupSection.mjs":{"id":"(app-pages-browser)/./node_modules/@mantine/core/esm/components/ActionIcon/ActionIconGroupSection/ActionIconGroupSection.mjs","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\svp\\frontend\\medicine\\node_modules\\@mantine\\core\\esm\\components\\Affix\\Affix.mjs":{"id":"(app-pages-browser)/./node_modules/@mantine/core/esm/components/Affix/Affix.mjs","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\svp\\frontend\\medicine\\node_modules\\@mantine\\core\\esm\\components\\Alert\\Alert.mjs":{"id":"(app-pages-browser)/./node_modules/@mantine/core/esm/components/Alert/Alert.mjs","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\svp\\frontend\\medicine\\node_modules\\@mantine\\core\\esm\\components\\Anchor\\Anchor.mjs":{"id":"(app-pages-browser)/./node_modules/@mantine/core/esm/components/Anchor/Anchor.mjs","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\svp\\frontend\\medicine\\node_modules\\@mantine\\core\\esm\\components\\AngleSlider\\AngleSlider.mjs":{"id":"(app-pages-browser)/./node_modules/@mantine/core/esm/components/AngleSlider/AngleSlider.mjs","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\svp\\frontend\\medicine\\node_modules\\@mantine\\core\\esm\\components\\AppShell\\AppShell.mjs":{"id":"(app-pages-browser)/./node_modules/@mantine/core/esm/components/AppShell/AppShell.mjs","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\svp\\frontend\\medicine\\node_modules\\@mantine\\core\\esm\\components\\AppShell\\AppShellAside\\AppShellAside.mjs":{"id":"(app-pages-browser)/./node_modules/@mantine/core/esm/components/AppShell/AppShellAside/AppShellAside.mjs","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\svp\\frontend\\medicine\\node_modules\\@mantine\\core\\esm\\components\\AppShell\\AppShellFooter\\AppShellFooter.mjs":{"id":"(app-pages-browser)/./node_modules/@mantine/core/esm/components/AppShell/AppShellFooter/AppShellFooter.mjs","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\svp\\frontend\\medicine\\node_modules\\@mantine\\core\\esm\\components\\AppShell\\AppShellHeader\\AppShellHeader.mjs":{"id":"(app-pages-browser)/./node_modules/@mantine/core/esm/components/AppShell/AppShellHeader/AppShellHeader.mjs","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\svp\\frontend\\medicine\\node_modules\\@mantine\\core\\esm\\components\\AppShell\\AppShellMain\\AppShellMain.mjs":{"id":"(app-pages-browser)/./node_modules/@mantine/core/esm/components/AppShell/AppShellMain/AppShellMain.mjs","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\svp\\frontend\\medicine\\node_modules\\@mantine\\core\\esm\\components\\AppShell\\AppShellNavbar\\AppShellNavbar.mjs":{"id":"(app-pages-browser)/./node_modules/@mantine/core/esm/components/AppShell/AppShellNavbar/AppShellNavbar.mjs","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\svp\\frontend\\medicine\\node_modules\\@mantine\\core\\esm\\components\\AppShell\\AppShellSection\\AppShellSection.mjs":{"id":"(app-pages-browser)/./node_modules/@mantine/core/esm/components/AppShell/AppShellSection/AppShellSection.mjs","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\svp\\frontend\\medicine\\node_modules\\@mantine\\core\\esm\\components\\AspectRatio\\AspectRatio.mjs":{"id":"(app-pages-browser)/./node_modules/@mantine/core/esm/components/AspectRatio/AspectRatio.mjs","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\svp\\frontend\\medicine\\node_modules\\@mantine\\core\\esm\\components\\Autocomplete\\Autocomplete.mjs":{"id":"(app-pages-browser)/./node_modules/@mantine/core/esm/components/Autocomplete/Autocomplete.mjs","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\svp\\frontend\\medicine\\node_modules\\@mantine\\core\\esm\\components\\Avatar\\Avatar.mjs":{"id":"(app-pages-browser)/./node_modules/@mantine/core/esm/components/Avatar/Avatar.mjs","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\svp\\frontend\\medicine\\node_modules\\@mantine\\core\\esm\\components\\Avatar\\AvatarGroup\\AvatarGroup.mjs":{"id":"(app-pages-browser)/./node_modules/@mantine/core/esm/components/Avatar/AvatarGroup/AvatarGroup.mjs","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\svp\\frontend\\medicine\\node_modules\\@mantine\\core\\esm\\components\\BackgroundImage\\BackgroundImage.mjs":{"id":"(app-pages-browser)/./node_modules/@mantine/core/esm/components/BackgroundImage/BackgroundImage.mjs","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\svp\\frontend\\medicine\\node_modules\\@mantine\\core\\esm\\components\\Badge\\Badge.mjs":{"id":"(app-pages-browser)/./node_modules/@mantine/core/esm/components/Badge/Badge.mjs","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\svp\\frontend\\medicine\\node_modules\\@mantine\\core\\esm\\components\\Blockquote\\Blockquote.mjs":{"id":"(app-pages-browser)/./node_modules/@mantine/core/esm/components/Blockquote/Blockquote.mjs","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\svp\\frontend\\medicine\\node_modules\\@mantine\\core\\esm\\components\\Breadcrumbs\\Breadcrumbs.mjs":{"id":"(app-pages-browser)/./node_modules/@mantine/core/esm/components/Breadcrumbs/Breadcrumbs.mjs","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\svp\\frontend\\medicine\\node_modules\\@mantine\\core\\esm\\components\\Burger\\Burger.mjs":{"id":"(app-pages-browser)/./node_modules/@mantine/core/esm/components/Burger/Burger.mjs","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\svp\\frontend\\medicine\\node_modules\\@mantine\\core\\esm\\components\\Button\\Button.mjs":{"id":"(app-pages-browser)/./node_modules/@mantine/core/esm/components/Button/Button.mjs","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\svp\\frontend\\medicine\\node_modules\\@mantine\\core\\esm\\components\\Button\\ButtonGroup\\ButtonGroup.mjs":{"id":"(app-pages-browser)/./node_modules/@mantine/core/esm/components/Button/ButtonGroup/ButtonGroup.mjs","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\svp\\frontend\\medicine\\node_modules\\@mantine\\core\\esm\\components\\Button\\ButtonGroupSection\\ButtonGroupSection.mjs":{"id":"(app-pages-browser)/./node_modules/@mantine/core/esm/components/Button/ButtonGroupSection/ButtonGroupSection.mjs","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\svp\\frontend\\medicine\\node_modules\\@mantine\\core\\esm\\components\\Card\\Card.mjs":{"id":"(app-pages-browser)/./node_modules/@mantine/core/esm/components/Card/Card.mjs","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\svp\\frontend\\medicine\\node_modules\\@mantine\\core\\esm\\components\\Card\\CardSection\\CardSection.mjs":{"id":"(app-pages-browser)/./node_modules/@mantine/core/esm/components/Card/CardSection/CardSection.mjs","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\svp\\frontend\\medicine\\node_modules\\@mantine\\core\\esm\\components\\Center\\Center.mjs":{"id":"(app-pages-browser)/./node_modules/@mantine/core/esm/components/Center/Center.mjs","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\svp\\frontend\\medicine\\node_modules\\@mantine\\core\\esm\\components\\Checkbox\\Checkbox.mjs":{"id":"(app-pages-browser)/./node_modules/@mantine/core/esm/components/Checkbox/Checkbox.mjs","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\svp\\frontend\\medicine\\node_modules\\@mantine\\core\\esm\\components\\Checkbox\\CheckboxCard\\CheckboxCard.context.mjs":{"id":"(app-pages-browser)/./node_modules/@mantine/core/esm/components/Checkbox/CheckboxCard/CheckboxCard.context.mjs","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\svp\\frontend\\medicine\\node_modules\\@mantine\\core\\esm\\components\\Checkbox\\CheckboxCard\\CheckboxCard.mjs":{"id":"(app-pages-browser)/./node_modules/@mantine/core/esm/components/Checkbox/CheckboxCard/CheckboxCard.mjs","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\svp\\frontend\\medicine\\node_modules\\@mantine\\core\\esm\\components\\Checkbox\\CheckboxGroup.context.mjs":{"id":"(app-pages-browser)/./node_modules/@mantine/core/esm/components/Checkbox/CheckboxGroup.context.mjs","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\svp\\frontend\\medicine\\node_modules\\@mantine\\core\\esm\\components\\Checkbox\\CheckboxGroup\\CheckboxGroup.mjs":{"id":"(app-pages-browser)/./node_modules/@mantine/core/esm/components/Checkbox/CheckboxGroup/CheckboxGroup.mjs","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\svp\\frontend\\medicine\\node_modules\\@mantine\\core\\esm\\components\\Checkbox\\CheckboxIndicator\\CheckboxIndicator.mjs":{"id":"(app-pages-browser)/./node_modules/@mantine/core/esm/components/Checkbox/CheckboxIndicator/CheckboxIndicator.mjs","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\svp\\frontend\\medicine\\node_modules\\@mantine\\core\\esm\\components\\Checkbox\\CheckIcon.mjs":{"id":"(app-pages-browser)/./node_modules/@mantine/core/esm/components/Checkbox/CheckIcon.mjs","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\svp\\frontend\\medicine\\node_modules\\@mantine\\core\\esm\\components\\Chip\\Chip.mjs":{"id":"(app-pages-browser)/./node_modules/@mantine/core/esm/components/Chip/Chip.mjs","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\svp\\frontend\\medicine\\node_modules\\@mantine\\core\\esm\\components\\Chip\\ChipGroup\\ChipGroup.mjs":{"id":"(app-pages-browser)/./node_modules/@mantine/core/esm/components/Chip/ChipGroup/ChipGroup.mjs","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\svp\\frontend\\medicine\\node_modules\\@mantine\\core\\esm\\components\\CloseButton\\CloseButton.mjs":{"id":"(app-pages-browser)/./node_modules/@mantine/core/esm/components/CloseButton/CloseButton.mjs","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\svp\\frontend\\medicine\\node_modules\\@mantine\\core\\esm\\components\\CloseButton\\CloseIcon.mjs":{"id":"(app-pages-browser)/./node_modules/@mantine/core/esm/components/CloseButton/CloseIcon.mjs","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\svp\\frontend\\medicine\\node_modules\\@mantine\\core\\esm\\components\\Code\\Code.mjs":{"id":"(app-pages-browser)/./node_modules/@mantine/core/esm/components/Code/Code.mjs","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\svp\\frontend\\medicine\\node_modules\\@mantine\\core\\esm\\components\\Collapse\\Collapse.mjs":{"id":"(app-pages-browser)/./node_modules/@mantine/core/esm/components/Collapse/Collapse.mjs","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\svp\\frontend\\medicine\\node_modules\\@mantine\\core\\esm\\components\\ColorInput\\ColorInput.mjs":{"id":"(app-pages-browser)/./node_modules/@mantine/core/esm/components/ColorInput/ColorInput.mjs","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\svp\\frontend\\medicine\\node_modules\\@mantine\\core\\esm\\components\\ColorPicker\\AlphaSlider\\AlphaSlider.mjs":{"id":"(app-pages-browser)/./node_modules/@mantine/core/esm/components/ColorPicker/AlphaSlider/AlphaSlider.mjs","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\svp\\frontend\\medicine\\node_modules\\@mantine\\core\\esm\\components\\ColorPicker\\ColorPicker.mjs":{"id":"(app-pages-browser)/./node_modules/@mantine/core/esm/components/ColorPicker/ColorPicker.mjs","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\svp\\frontend\\medicine\\node_modules\\@mantine\\core\\esm\\components\\ColorPicker\\converters\\converters.mjs":{"id":"(app-pages-browser)/./node_modules/@mantine/core/esm/components/ColorPicker/converters/converters.mjs","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\svp\\frontend\\medicine\\node_modules\\@mantine\\core\\esm\\components\\ColorPicker\\converters\\parsers.mjs":{"id":"(app-pages-browser)/./node_modules/@mantine/core/esm/components/ColorPicker/converters/parsers.mjs","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\svp\\frontend\\medicine\\node_modules\\@mantine\\core\\esm\\components\\ColorPicker\\HueSlider\\HueSlider.mjs":{"id":"(app-pages-browser)/./node_modules/@mantine/core/esm/components/ColorPicker/HueSlider/HueSlider.mjs","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\svp\\frontend\\medicine\\node_modules\\@mantine\\core\\esm\\components\\ColorSwatch\\ColorSwatch.mjs":{"id":"(app-pages-browser)/./node_modules/@mantine/core/esm/components/ColorSwatch/ColorSwatch.mjs","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\svp\\frontend\\medicine\\node_modules\\@mantine\\core\\esm\\components\\Combobox\\Combobox.mjs":{"id":"(app-pages-browser)/./node_modules/@mantine/core/esm/components/Combobox/Combobox.mjs","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\svp\\frontend\\medicine\\node_modules\\@mantine\\core\\esm\\components\\Combobox\\ComboboxChevron\\ComboboxChevron.mjs":{"id":"(app-pages-browser)/./node_modules/@mantine/core/esm/components/Combobox/ComboboxChevron/ComboboxChevron.mjs","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\svp\\frontend\\medicine\\node_modules\\@mantine\\core\\esm\\components\\Combobox\\ComboboxClearButton\\ComboboxClearButton.mjs":{"id":"(app-pages-browser)/./node_modules/@mantine/core/esm/components/Combobox/ComboboxClearButton/ComboboxClearButton.mjs","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\svp\\frontend\\medicine\\node_modules\\@mantine\\core\\esm\\components\\Combobox\\ComboboxDropdown\\ComboboxDropdown.mjs":{"id":"(app-pages-browser)/./node_modules/@mantine/core/esm/components/Combobox/ComboboxDropdown/ComboboxDropdown.mjs","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\svp\\frontend\\medicine\\node_modules\\@mantine\\core\\esm\\components\\Combobox\\ComboboxDropdownTarget\\ComboboxDropdownTarget.mjs":{"id":"(app-pages-browser)/./node_modules/@mantine/core/esm/components/Combobox/ComboboxDropdownTarget/ComboboxDropdownTarget.mjs","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\svp\\frontend\\medicine\\node_modules\\@mantine\\core\\esm\\components\\Combobox\\ComboboxEmpty\\ComboboxEmpty.mjs":{"id":"(app-pages-browser)/./node_modules/@mantine/core/esm/components/Combobox/ComboboxEmpty/ComboboxEmpty.mjs","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\svp\\frontend\\medicine\\node_modules\\@mantine\\core\\esm\\components\\Combobox\\ComboboxEventsTarget\\ComboboxEventsTarget.mjs":{"id":"(app-pages-browser)/./node_modules/@mantine/core/esm/components/Combobox/ComboboxEventsTarget/ComboboxEventsTarget.mjs","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\svp\\frontend\\medicine\\node_modules\\@mantine\\core\\esm\\components\\Combobox\\ComboboxFooter\\ComboboxFooter.mjs":{"id":"(app-pages-browser)/./node_modules/@mantine/core/esm/components/Combobox/ComboboxFooter/ComboboxFooter.mjs","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\svp\\frontend\\medicine\\node_modules\\@mantine\\core\\esm\\components\\Combobox\\ComboboxGroup\\ComboboxGroup.mjs":{"id":"(app-pages-browser)/./node_modules/@mantine/core/esm/components/Combobox/ComboboxGroup/ComboboxGroup.mjs","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\svp\\frontend\\medicine\\node_modules\\@mantine\\core\\esm\\components\\Combobox\\ComboboxHeader\\ComboboxHeader.mjs":{"id":"(app-pages-browser)/./node_modules/@mantine/core/esm/components/Combobox/ComboboxHeader/ComboboxHeader.mjs","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\svp\\frontend\\medicine\\node_modules\\@mantine\\core\\esm\\components\\Combobox\\ComboboxHiddenInput\\ComboboxHiddenInput.mjs":{"id":"(app-pages-browser)/./node_modules/@mantine/core/esm/components/Combobox/ComboboxHiddenInput/ComboboxHiddenInput.mjs","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\svp\\frontend\\medicine\\node_modules\\@mantine\\core\\esm\\components\\Combobox\\ComboboxOption\\ComboboxOption.mjs":{"id":"(app-pages-browser)/./node_modules/@mantine/core/esm/components/Combobox/ComboboxOption/ComboboxOption.mjs","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\svp\\frontend\\medicine\\node_modules\\@mantine\\core\\esm\\components\\Combobox\\ComboboxOptions\\ComboboxOptions.mjs":{"id":"(app-pages-browser)/./node_modules/@mantine/core/esm/components/Combobox/ComboboxOptions/ComboboxOptions.mjs","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\svp\\frontend\\medicine\\node_modules\\@mantine\\core\\esm\\components\\Combobox\\ComboboxSearch\\ComboboxSearch.mjs":{"id":"(app-pages-browser)/./node_modules/@mantine/core/esm/components/Combobox/ComboboxSearch/ComboboxSearch.mjs","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\svp\\frontend\\medicine\\node_modules\\@mantine\\core\\esm\\components\\Combobox\\ComboboxTarget\\ComboboxTarget.mjs":{"id":"(app-pages-browser)/./node_modules/@mantine/core/esm/components/Combobox/ComboboxTarget/ComboboxTarget.mjs","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\svp\\frontend\\medicine\\node_modules\\@mantine\\core\\esm\\components\\Combobox\\get-options-lockup\\get-options-lockup.mjs":{"id":"(app-pages-browser)/./node_modules/@mantine/core/esm/components/Combobox/get-options-lockup/get-options-lockup.mjs","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\svp\\frontend\\medicine\\node_modules\\@mantine\\core\\esm\\components\\Combobox\\get-parsed-combobox-data\\get-parsed-combobox-data.mjs":{"id":"(app-pages-browser)/./node_modules/@mantine/core/esm/components/Combobox/get-parsed-combobox-data/get-parsed-combobox-data.mjs","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\svp\\frontend\\medicine\\node_modules\\@mantine\\core\\esm\\components\\Combobox\\OptionsDropdown\\default-options-filter.mjs":{"id":"(app-pages-browser)/./node_modules/@mantine/core/esm/components/Combobox/OptionsDropdown/default-options-filter.mjs","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\svp\\frontend\\medicine\\node_modules\\@mantine\\core\\esm\\components\\Combobox\\OptionsDropdown\\is-options-group.mjs":{"id":"(app-pages-browser)/./node_modules/@mantine/core/esm/components/Combobox/OptionsDropdown/is-options-group.mjs","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\svp\\frontend\\medicine\\node_modules\\@mantine\\core\\esm\\components\\Combobox\\OptionsDropdown\\OptionsDropdown.mjs":{"id":"(app-pages-browser)/./node_modules/@mantine/core/esm/components/Combobox/OptionsDropdown/OptionsDropdown.mjs","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\svp\\frontend\\medicine\\node_modules\\@mantine\\core\\esm\\components\\Combobox\\use-combobox-target-props\\use-combobox-target-props.mjs":{"id":"(app-pages-browser)/./node_modules/@mantine/core/esm/components/Combobox/use-combobox-target-props/use-combobox-target-props.mjs","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\svp\\frontend\\medicine\\node_modules\\@mantine\\core\\esm\\components\\Combobox\\use-combobox\\use-combobox.mjs":{"id":"(app-pages-browser)/./node_modules/@mantine/core/esm/components/Combobox/use-combobox/use-combobox.mjs","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\svp\\frontend\\medicine\\node_modules\\@mantine\\core\\esm\\components\\Combobox\\use-combobox\\use-virtualized-combobox.mjs":{"id":"(app-pages-browser)/./node_modules/@mantine/core/esm/components/Combobox/use-combobox/use-virtualized-combobox.mjs","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\svp\\frontend\\medicine\\node_modules\\@mantine\\core\\esm\\components\\Container\\Container.mjs":{"id":"(app-pages-browser)/./node_modules/@mantine/core/esm/components/Container/Container.mjs","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\svp\\frontend\\medicine\\node_modules\\@mantine\\core\\esm\\components\\CopyButton\\CopyButton.mjs":{"id":"(app-pages-browser)/./node_modules/@mantine/core/esm/components/CopyButton/CopyButton.mjs","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\svp\\frontend\\medicine\\node_modules\\@mantine\\core\\esm\\components\\Dialog\\Dialog.mjs":{"id":"(app-pages-browser)/./node_modules/@mantine/core/esm/components/Dialog/Dialog.mjs","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\svp\\frontend\\medicine\\node_modules\\@mantine\\core\\esm\\components\\Divider\\Divider.mjs":{"id":"(app-pages-browser)/./node_modules/@mantine/core/esm/components/Divider/Divider.mjs","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\svp\\frontend\\medicine\\node_modules\\@mantine\\core\\esm\\components\\Drawer\\Drawer.mjs":{"id":"(app-pages-browser)/./node_modules/@mantine/core/esm/components/Drawer/Drawer.mjs","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\svp\\frontend\\medicine\\node_modules\\@mantine\\core\\esm\\components\\Drawer\\DrawerBody.mjs":{"id":"(app-pages-browser)/./node_modules/@mantine/core/esm/components/Drawer/DrawerBody.mjs","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\svp\\frontend\\medicine\\node_modules\\@mantine\\core\\esm\\components\\Drawer\\DrawerCloseButton.mjs":{"id":"(app-pages-browser)/./node_modules/@mantine/core/esm/components/Drawer/DrawerCloseButton.mjs","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\svp\\frontend\\medicine\\node_modules\\@mantine\\core\\esm\\components\\Drawer\\DrawerContent.mjs":{"id":"(app-pages-browser)/./node_modules/@mantine/core/esm/components/Drawer/DrawerContent.mjs","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\svp\\frontend\\medicine\\node_modules\\@mantine\\core\\esm\\components\\Drawer\\DrawerHeader.mjs":{"id":"(app-pages-browser)/./node_modules/@mantine/core/esm/components/Drawer/DrawerHeader.mjs","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\svp\\frontend\\medicine\\node_modules\\@mantine\\core\\esm\\components\\Drawer\\DrawerOverlay.mjs":{"id":"(app-pages-browser)/./node_modules/@mantine/core/esm/components/Drawer/DrawerOverlay.mjs","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\svp\\frontend\\medicine\\node_modules\\@mantine\\core\\esm\\components\\Drawer\\DrawerRoot.mjs":{"id":"(app-pages-browser)/./node_modules/@mantine/core/esm/components/Drawer/DrawerRoot.mjs","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\svp\\frontend\\medicine\\node_modules\\@mantine\\core\\esm\\components\\Drawer\\DrawerStack.mjs":{"id":"(app-pages-browser)/./node_modules/@mantine/core/esm/components/Drawer/DrawerStack.mjs","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\svp\\frontend\\medicine\\node_modules\\@mantine\\core\\esm\\components\\Drawer\\DrawerTitle.mjs":{"id":"(app-pages-browser)/./node_modules/@mantine/core/esm/components/Drawer/DrawerTitle.mjs","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\svp\\frontend\\medicine\\node_modules\\@mantine\\core\\esm\\components\\Fieldset\\Fieldset.mjs":{"id":"(app-pages-browser)/./node_modules/@mantine/core/esm/components/Fieldset/Fieldset.mjs","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\svp\\frontend\\medicine\\node_modules\\@mantine\\core\\esm\\components\\FileButton\\FileButton.mjs":{"id":"(app-pages-browser)/./node_modules/@mantine/core/esm/components/FileButton/FileButton.mjs","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\svp\\frontend\\medicine\\node_modules\\@mantine\\core\\esm\\components\\FileInput\\FileInput.mjs":{"id":"(app-pages-browser)/./node_modules/@mantine/core/esm/components/FileInput/FileInput.mjs","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\svp\\frontend\\medicine\\node_modules\\@mantine\\core\\esm\\components\\Flex\\flex-props.mjs":{"id":"(app-pages-browser)/./node_modules/@mantine/core/esm/components/Flex/flex-props.mjs","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\svp\\frontend\\medicine\\node_modules\\@mantine\\core\\esm\\components\\Flex\\Flex.mjs":{"id":"(app-pages-browser)/./node_modules/@mantine/core/esm/components/Flex/Flex.mjs","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\svp\\frontend\\medicine\\node_modules\\@mantine\\core\\esm\\components\\Floating\\FloatingArrow\\FloatingArrow.mjs":{"id":"(app-pages-browser)/./node_modules/@mantine/core/esm/components/Floating/FloatingArrow/FloatingArrow.mjs","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\svp\\frontend\\medicine\\node_modules\\@mantine\\core\\esm\\components\\Floating\\get-floating-position\\get-floating-position.mjs":{"id":"(app-pages-browser)/./node_modules/@mantine/core/esm/components/Floating/get-floating-position/get-floating-position.mjs","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\svp\\frontend\\medicine\\node_modules\\@mantine\\core\\esm\\components\\Floating\\use-delayed-hover.mjs":{"id":"(app-pages-browser)/./node_modules/@mantine/core/esm/components/Floating/use-delayed-hover.mjs","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\svp\\frontend\\medicine\\node_modules\\@mantine\\core\\esm\\components\\Floating\\use-floating-auto-update.mjs":{"id":"(app-pages-browser)/./node_modules/@mantine/core/esm/components/Floating/use-floating-auto-update.mjs","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\svp\\frontend\\medicine\\node_modules\\@mantine\\core\\esm\\components\\FloatingIndicator\\FloatingIndicator.mjs":{"id":"(app-pages-browser)/./node_modules/@mantine/core/esm/components/FloatingIndicator/FloatingIndicator.mjs","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\svp\\frontend\\medicine\\node_modules\\@mantine\\core\\esm\\components\\FocusTrap\\FocusTrap.mjs":{"id":"(app-pages-browser)/./node_modules/@mantine/core/esm/components/FocusTrap/FocusTrap.mjs","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\svp\\frontend\\medicine\\node_modules\\@mantine\\core\\esm\\components\\Grid\\Grid.mjs":{"id":"(app-pages-browser)/./node_modules/@mantine/core/esm/components/Grid/Grid.mjs","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\svp\\frontend\\medicine\\node_modules\\@mantine\\core\\esm\\components\\Grid\\GridCol\\GridCol.mjs":{"id":"(app-pages-browser)/./node_modules/@mantine/core/esm/components/Grid/GridCol/GridCol.mjs","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\svp\\frontend\\medicine\\node_modules\\@mantine\\core\\esm\\components\\Group\\Group.mjs":{"id":"(app-pages-browser)/./node_modules/@mantine/core/esm/components/Group/Group.mjs","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\svp\\frontend\\medicine\\node_modules\\@mantine\\core\\esm\\components\\Highlight\\Highlight.mjs":{"id":"(app-pages-browser)/./node_modules/@mantine/core/esm/components/Highlight/Highlight.mjs","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\svp\\frontend\\medicine\\node_modules\\@mantine\\core\\esm\\components\\HoverCard\\HoverCard.mjs":{"id":"(app-pages-browser)/./node_modules/@mantine/core/esm/components/HoverCard/HoverCard.mjs","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\svp\\frontend\\medicine\\node_modules\\@mantine\\core\\esm\\components\\HoverCard\\HoverCardDropdown\\HoverCardDropdown.mjs":{"id":"(app-pages-browser)/./node_modules/@mantine/core/esm/components/HoverCard/HoverCardDropdown/HoverCardDropdown.mjs","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\svp\\frontend\\medicine\\node_modules\\@mantine\\core\\esm\\components\\HoverCard\\HoverCardTarget\\HoverCardTarget.mjs":{"id":"(app-pages-browser)/./node_modules/@mantine/core/esm/components/HoverCard/HoverCardTarget/HoverCardTarget.mjs","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\svp\\frontend\\medicine\\node_modules\\@mantine\\core\\esm\\components\\Image\\Image.mjs":{"id":"(app-pages-browser)/./node_modules/@mantine/core/esm/components/Image/Image.mjs","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\svp\\frontend\\medicine\\node_modules\\@mantine\\core\\esm\\components\\Indicator\\Indicator.mjs":{"id":"(app-pages-browser)/./node_modules/@mantine/core/esm/components/Indicator/Indicator.mjs","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\svp\\frontend\\medicine\\node_modules\\@mantine\\core\\esm\\components\\Input\\Input.mjs":{"id":"(app-pages-browser)/./node_modules/@mantine/core/esm/components/Input/Input.mjs","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\svp\\frontend\\medicine\\node_modules\\@mantine\\core\\esm\\components\\Input\\InputClearButton\\InputClearButton.mjs":{"id":"(app-pages-browser)/./node_modules/@mantine/core/esm/components/Input/InputClearButton/InputClearButton.mjs","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\svp\\frontend\\medicine\\node_modules\\@mantine\\core\\esm\\components\\Input\\InputDescription\\InputDescription.mjs":{"id":"(app-pages-browser)/./node_modules/@mantine/core/esm/components/Input/InputDescription/InputDescription.mjs","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\svp\\frontend\\medicine\\node_modules\\@mantine\\core\\esm\\components\\Input\\InputError\\InputError.mjs":{"id":"(app-pages-browser)/./node_modules/@mantine/core/esm/components/Input/InputError/InputError.mjs","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\svp\\frontend\\medicine\\node_modules\\@mantine\\core\\esm\\components\\Input\\InputLabel\\InputLabel.mjs":{"id":"(app-pages-browser)/./node_modules/@mantine/core/esm/components/Input/InputLabel/InputLabel.mjs","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\svp\\frontend\\medicine\\node_modules\\@mantine\\core\\esm\\components\\Input\\InputPlaceholder\\InputPlaceholder.mjs":{"id":"(app-pages-browser)/./node_modules/@mantine/core/esm/components/Input/InputPlaceholder/InputPlaceholder.mjs","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\svp\\frontend\\medicine\\node_modules\\@mantine\\core\\esm\\components\\Input\\InputWrapper.context.mjs":{"id":"(app-pages-browser)/./node_modules/@mantine/core/esm/components/Input/InputWrapper.context.mjs","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\svp\\frontend\\medicine\\node_modules\\@mantine\\core\\esm\\components\\Input\\InputWrapper\\InputWrapper.mjs":{"id":"(app-pages-browser)/./node_modules/@mantine/core/esm/components/Input/InputWrapper/InputWrapper.mjs","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\svp\\frontend\\medicine\\node_modules\\@mantine\\core\\esm\\components\\Input\\use-input-props.mjs":{"id":"(app-pages-browser)/./node_modules/@mantine/core/esm/components/Input/use-input-props.mjs","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\svp\\frontend\\medicine\\node_modules\\@mantine\\core\\esm\\components\\InputBase\\InputBase.mjs":{"id":"(app-pages-browser)/./node_modules/@mantine/core/esm/components/InputBase/InputBase.mjs","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\svp\\frontend\\medicine\\node_modules\\@mantine\\core\\esm\\components\\JsonInput\\JsonInput.mjs":{"id":"(app-pages-browser)/./node_modules/@mantine/core/esm/components/JsonInput/JsonInput.mjs","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\svp\\frontend\\medicine\\node_modules\\@mantine\\core\\esm\\components\\Kbd\\Kbd.mjs":{"id":"(app-pages-browser)/./node_modules/@mantine/core/esm/components/Kbd/Kbd.mjs","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\svp\\frontend\\medicine\\node_modules\\@mantine\\core\\esm\\components\\List\\List.mjs":{"id":"(app-pages-browser)/./node_modules/@mantine/core/esm/components/List/List.mjs","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\svp\\frontend\\medicine\\node_modules\\@mantine\\core\\esm\\components\\List\\ListItem\\ListItem.mjs":{"id":"(app-pages-browser)/./node_modules/@mantine/core/esm/components/List/ListItem/ListItem.mjs","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\svp\\frontend\\medicine\\node_modules\\@mantine\\core\\esm\\components\\Loader\\Loader.mjs":{"id":"(app-pages-browser)/./node_modules/@mantine/core/esm/components/Loader/Loader.mjs","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\svp\\frontend\\medicine\\node_modules\\@mantine\\core\\esm\\components\\LoadingOverlay\\LoadingOverlay.mjs":{"id":"(app-pages-browser)/./node_modules/@mantine/core/esm/components/LoadingOverlay/LoadingOverlay.mjs","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\svp\\frontend\\medicine\\node_modules\\@mantine\\core\\esm\\components\\Mark\\Mark.mjs":{"id":"(app-pages-browser)/./node_modules/@mantine/core/esm/components/Mark/Mark.mjs","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\svp\\frontend\\medicine\\node_modules\\@mantine\\core\\esm\\components\\Menu\\Menu.mjs":{"id":"(app-pages-browser)/./node_modules/@mantine/core/esm/components/Menu/Menu.mjs","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\svp\\frontend\\medicine\\node_modules\\@mantine\\core\\esm\\components\\Menu\\MenuDivider\\MenuDivider.mjs":{"id":"(app-pages-browser)/./node_modules/@mantine/core/esm/components/Menu/MenuDivider/MenuDivider.mjs","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\svp\\frontend\\medicine\\node_modules\\@mantine\\core\\esm\\components\\Menu\\MenuDropdown\\MenuDropdown.mjs":{"id":"(app-pages-browser)/./node_modules/@mantine/core/esm/components/Menu/MenuDropdown/MenuDropdown.mjs","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\svp\\frontend\\medicine\\node_modules\\@mantine\\core\\esm\\components\\Menu\\MenuItem\\MenuItem.mjs":{"id":"(app-pages-browser)/./node_modules/@mantine/core/esm/components/Menu/MenuItem/MenuItem.mjs","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\svp\\frontend\\medicine\\node_modules\\@mantine\\core\\esm\\components\\Menu\\MenuLabel\\MenuLabel.mjs":{"id":"(app-pages-browser)/./node_modules/@mantine/core/esm/components/Menu/MenuLabel/MenuLabel.mjs","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\svp\\frontend\\medicine\\node_modules\\@mantine\\core\\esm\\components\\Menu\\MenuTarget\\MenuTarget.mjs":{"id":"(app-pages-browser)/./node_modules/@mantine/core/esm/components/Menu/MenuTarget/MenuTarget.mjs","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\svp\\frontend\\medicine\\node_modules\\@mantine\\core\\esm\\components\\Modal\\Modal.mjs":{"id":"(app-pages-browser)/./node_modules/@mantine/core/esm/components/Modal/Modal.mjs","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\svp\\frontend\\medicine\\node_modules\\@mantine\\core\\esm\\components\\Modal\\ModalBody.mjs":{"id":"(app-pages-browser)/./node_modules/@mantine/core/esm/components/Modal/ModalBody.mjs","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\svp\\frontend\\medicine\\node_modules\\@mantine\\core\\esm\\components\\Modal\\ModalCloseButton.mjs":{"id":"(app-pages-browser)/./node_modules/@mantine/core/esm/components/Modal/ModalCloseButton.mjs","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\svp\\frontend\\medicine\\node_modules\\@mantine\\core\\esm\\components\\Modal\\ModalContent.mjs":{"id":"(app-pages-browser)/./node_modules/@mantine/core/esm/components/Modal/ModalContent.mjs","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\svp\\frontend\\medicine\\node_modules\\@mantine\\core\\esm\\components\\Modal\\ModalHeader.mjs":{"id":"(app-pages-browser)/./node_modules/@mantine/core/esm/components/Modal/ModalHeader.mjs","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\svp\\frontend\\medicine\\node_modules\\@mantine\\core\\esm\\components\\Modal\\ModalOverlay.mjs":{"id":"(app-pages-browser)/./node_modules/@mantine/core/esm/components/Modal/ModalOverlay.mjs","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\svp\\frontend\\medicine\\node_modules\\@mantine\\core\\esm\\components\\Modal\\ModalRoot.mjs":{"id":"(app-pages-browser)/./node_modules/@mantine/core/esm/components/Modal/ModalRoot.mjs","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\svp\\frontend\\medicine\\node_modules\\@mantine\\core\\esm\\components\\Modal\\ModalStack.mjs":{"id":"(app-pages-browser)/./node_modules/@mantine/core/esm/components/Modal/ModalStack.mjs","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\svp\\frontend\\medicine\\node_modules\\@mantine\\core\\esm\\components\\Modal\\ModalTitle.mjs":{"id":"(app-pages-browser)/./node_modules/@mantine/core/esm/components/Modal/ModalTitle.mjs","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\svp\\frontend\\medicine\\node_modules\\@mantine\\core\\esm\\components\\Modal\\use-modals-stack.mjs":{"id":"(app-pages-browser)/./node_modules/@mantine/core/esm/components/Modal/use-modals-stack.mjs","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\svp\\frontend\\medicine\\node_modules\\@mantine\\core\\esm\\components\\ModalBase\\ModalBase.mjs":{"id":"(app-pages-browser)/./node_modules/@mantine/core/esm/components/ModalBase/ModalBase.mjs","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\svp\\frontend\\medicine\\node_modules\\@mantine\\core\\esm\\components\\ModalBase\\ModalBaseBody.mjs":{"id":"(app-pages-browser)/./node_modules/@mantine/core/esm/components/ModalBase/ModalBaseBody.mjs","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\svp\\frontend\\medicine\\node_modules\\@mantine\\core\\esm\\components\\ModalBase\\ModalBaseCloseButton.mjs":{"id":"(app-pages-browser)/./node_modules/@mantine/core/esm/components/ModalBase/ModalBaseCloseButton.mjs","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\svp\\frontend\\medicine\\node_modules\\@mantine\\core\\esm\\components\\ModalBase\\ModalBaseContent.mjs":{"id":"(app-pages-browser)/./node_modules/@mantine/core/esm/components/ModalBase/ModalBaseContent.mjs","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\svp\\frontend\\medicine\\node_modules\\@mantine\\core\\esm\\components\\ModalBase\\ModalBaseHeader.mjs":{"id":"(app-pages-browser)/./node_modules/@mantine/core/esm/components/ModalBase/ModalBaseHeader.mjs","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\svp\\frontend\\medicine\\node_modules\\@mantine\\core\\esm\\components\\ModalBase\\ModalBaseOverlay.mjs":{"id":"(app-pages-browser)/./node_modules/@mantine/core/esm/components/ModalBase/ModalBaseOverlay.mjs","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\svp\\frontend\\medicine\\node_modules\\@mantine\\core\\esm\\components\\ModalBase\\ModalBaseTitle.mjs":{"id":"(app-pages-browser)/./node_modules/@mantine/core/esm/components/ModalBase/ModalBaseTitle.mjs","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\svp\\frontend\\medicine\\node_modules\\@mantine\\core\\esm\\components\\ModalBase\\NativeScrollArea.mjs":{"id":"(app-pages-browser)/./node_modules/@mantine/core/esm/components/ModalBase/NativeScrollArea.mjs","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\svp\\frontend\\medicine\\node_modules\\@mantine\\core\\esm\\components\\MultiSelect\\MultiSelect.mjs":{"id":"(app-pages-browser)/./node_modules/@mantine/core/esm/components/MultiSelect/MultiSelect.mjs","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\svp\\frontend\\medicine\\node_modules\\@mantine\\core\\esm\\components\\NativeSelect\\NativeSelect.mjs":{"id":"(app-pages-browser)/./node_modules/@mantine/core/esm/components/NativeSelect/NativeSelect.mjs","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\svp\\frontend\\medicine\\node_modules\\@mantine\\core\\esm\\components\\NavLink\\NavLink.mjs":{"id":"(app-pages-browser)/./node_modules/@mantine/core/esm/components/NavLink/NavLink.mjs","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\svp\\frontend\\medicine\\node_modules\\@mantine\\core\\esm\\components\\Notification\\Notification.mjs":{"id":"(app-pages-browser)/./node_modules/@mantine/core/esm/components/Notification/Notification.mjs","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\svp\\frontend\\medicine\\node_modules\\@mantine\\core\\esm\\components\\NumberFormatter\\NumberFormatter.mjs":{"id":"(app-pages-browser)/./node_modules/@mantine/core/esm/components/NumberFormatter/NumberFormatter.mjs","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\svp\\frontend\\medicine\\node_modules\\@mantine\\core\\esm\\components\\NumberInput\\NumberInput.mjs":{"id":"(app-pages-browser)/./node_modules/@mantine/core/esm/components/NumberInput/NumberInput.mjs","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\svp\\frontend\\medicine\\node_modules\\@mantine\\core\\esm\\components\\Overlay\\Overlay.mjs":{"id":"(app-pages-browser)/./node_modules/@mantine/core/esm/components/Overlay/Overlay.mjs","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\svp\\frontend\\medicine\\node_modules\\@mantine\\core\\esm\\components\\Pagination\\Pagination.mjs":{"id":"(app-pages-browser)/./node_modules/@mantine/core/esm/components/Pagination/Pagination.mjs","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\svp\\frontend\\medicine\\node_modules\\@mantine\\core\\esm\\components\\Pagination\\PaginationControl\\PaginationControl.mjs":{"id":"(app-pages-browser)/./node_modules/@mantine/core/esm/components/Pagination/PaginationControl/PaginationControl.mjs","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\svp\\frontend\\medicine\\node_modules\\@mantine\\core\\esm\\components\\Pagination\\PaginationDots\\PaginationDots.mjs":{"id":"(app-pages-browser)/./node_modules/@mantine/core/esm/components/Pagination/PaginationDots/PaginationDots.mjs","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\svp\\frontend\\medicine\\node_modules\\@mantine\\core\\esm\\components\\Pagination\\PaginationEdges\\PaginationEdges.mjs":{"id":"(app-pages-browser)/./node_modules/@mantine/core/esm/components/Pagination/PaginationEdges/PaginationEdges.mjs","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\svp\\frontend\\medicine\\node_modules\\@mantine\\core\\esm\\components\\Pagination\\PaginationItems\\PaginationItems.mjs":{"id":"(app-pages-browser)/./node_modules/@mantine/core/esm/components/Pagination/PaginationItems/PaginationItems.mjs","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\svp\\frontend\\medicine\\node_modules\\@mantine\\core\\esm\\components\\Pagination\\PaginationRoot\\PaginationRoot.mjs":{"id":"(app-pages-browser)/./node_modules/@mantine/core/esm/components/Pagination/PaginationRoot/PaginationRoot.mjs","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\svp\\frontend\\medicine\\node_modules\\@mantine\\core\\esm\\components\\Paper\\Paper.mjs":{"id":"(app-pages-browser)/./node_modules/@mantine/core/esm/components/Paper/Paper.mjs","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\svp\\frontend\\medicine\\node_modules\\@mantine\\core\\esm\\components\\PasswordInput\\PasswordInput.mjs":{"id":"(app-pages-browser)/./node_modules/@mantine/core/esm/components/PasswordInput/PasswordInput.mjs","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\svp\\frontend\\medicine\\node_modules\\@mantine\\core\\esm\\components\\Pill\\Pill.mjs":{"id":"(app-pages-browser)/./node_modules/@mantine/core/esm/components/Pill/Pill.mjs","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\svp\\frontend\\medicine\\node_modules\\@mantine\\core\\esm\\components\\Pill\\PillGroup\\PillGroup.mjs":{"id":"(app-pages-browser)/./node_modules/@mantine/core/esm/components/Pill/PillGroup/PillGroup.mjs","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\svp\\frontend\\medicine\\node_modules\\@mantine\\core\\esm\\components\\PillsInput\\PillsInput.mjs":{"id":"(app-pages-browser)/./node_modules/@mantine/core/esm/components/PillsInput/PillsInput.mjs","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\svp\\frontend\\medicine\\node_modules\\@mantine\\core\\esm\\components\\PillsInput\\PillsInputField\\PillsInputField.mjs":{"id":"(app-pages-browser)/./node_modules/@mantine/core/esm/components/PillsInput/PillsInputField/PillsInputField.mjs","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\svp\\frontend\\medicine\\node_modules\\@mantine\\core\\esm\\components\\PinInput\\PinInput.mjs":{"id":"(app-pages-browser)/./node_modules/@mantine/core/esm/components/PinInput/PinInput.mjs","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\svp\\frontend\\medicine\\node_modules\\@mantine\\core\\esm\\components\\Popover\\Popover.mjs":{"id":"(app-pages-browser)/./node_modules/@mantine/core/esm/components/Popover/Popover.mjs","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\svp\\frontend\\medicine\\node_modules\\@mantine\\core\\esm\\components\\Popover\\PopoverDropdown\\PopoverDropdown.mjs":{"id":"(app-pages-browser)/./node_modules/@mantine/core/esm/components/Popover/PopoverDropdown/PopoverDropdown.mjs","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\svp\\frontend\\medicine\\node_modules\\@mantine\\core\\esm\\components\\Popover\\PopoverTarget\\PopoverTarget.mjs":{"id":"(app-pages-browser)/./node_modules/@mantine/core/esm/components/Popover/PopoverTarget/PopoverTarget.mjs","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\svp\\frontend\\medicine\\node_modules\\@mantine\\core\\esm\\components\\Portal\\OptionalPortal.mjs":{"id":"(app-pages-browser)/./node_modules/@mantine/core/esm/components/Portal/OptionalPortal.mjs","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\svp\\frontend\\medicine\\node_modules\\@mantine\\core\\esm\\components\\Portal\\Portal.mjs":{"id":"(app-pages-browser)/./node_modules/@mantine/core/esm/components/Portal/Portal.mjs","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\svp\\frontend\\medicine\\node_modules\\@mantine\\core\\esm\\components\\Progress\\Progress.mjs":{"id":"(app-pages-browser)/./node_modules/@mantine/core/esm/components/Progress/Progress.mjs","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\svp\\frontend\\medicine\\node_modules\\@mantine\\core\\esm\\components\\Progress\\ProgressLabel\\ProgressLabel.mjs":{"id":"(app-pages-browser)/./node_modules/@mantine/core/esm/components/Progress/ProgressLabel/ProgressLabel.mjs","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\svp\\frontend\\medicine\\node_modules\\@mantine\\core\\esm\\components\\Progress\\ProgressRoot\\ProgressRoot.mjs":{"id":"(app-pages-browser)/./node_modules/@mantine/core/esm/components/Progress/ProgressRoot/ProgressRoot.mjs","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\svp\\frontend\\medicine\\node_modules\\@mantine\\core\\esm\\components\\Progress\\ProgressSection\\ProgressSection.mjs":{"id":"(app-pages-browser)/./node_modules/@mantine/core/esm/components/Progress/ProgressSection/ProgressSection.mjs","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\svp\\frontend\\medicine\\node_modules\\@mantine\\core\\esm\\components\\Radio\\Radio.mjs":{"id":"(app-pages-browser)/./node_modules/@mantine/core/esm/components/Radio/Radio.mjs","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\svp\\frontend\\medicine\\node_modules\\@mantine\\core\\esm\\components\\Radio\\RadioCard\\RadioCard.context.mjs":{"id":"(app-pages-browser)/./node_modules/@mantine/core/esm/components/Radio/RadioCard/RadioCard.context.mjs","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\svp\\frontend\\medicine\\node_modules\\@mantine\\core\\esm\\components\\Radio\\RadioCard\\RadioCard.mjs":{"id":"(app-pages-browser)/./node_modules/@mantine/core/esm/components/Radio/RadioCard/RadioCard.mjs","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\svp\\frontend\\medicine\\node_modules\\@mantine\\core\\esm\\components\\Radio\\RadioGroup\\RadioGroup.mjs":{"id":"(app-pages-browser)/./node_modules/@mantine/core/esm/components/Radio/RadioGroup/RadioGroup.mjs","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\svp\\frontend\\medicine\\node_modules\\@mantine\\core\\esm\\components\\Radio\\RadioIcon.mjs":{"id":"(app-pages-browser)/./node_modules/@mantine/core/esm/components/Radio/RadioIcon.mjs","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\svp\\frontend\\medicine\\node_modules\\@mantine\\core\\esm\\components\\Radio\\RadioIndicator\\RadioIndicator.mjs":{"id":"(app-pages-browser)/./node_modules/@mantine/core/esm/components/Radio/RadioIndicator/RadioIndicator.mjs","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\svp\\frontend\\medicine\\node_modules\\@mantine\\core\\esm\\components\\Rating\\Rating.mjs":{"id":"(app-pages-browser)/./node_modules/@mantine/core/esm/components/Rating/Rating.mjs","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\svp\\frontend\\medicine\\node_modules\\@mantine\\core\\esm\\components\\RingProgress\\RingProgress.mjs":{"id":"(app-pages-browser)/./node_modules/@mantine/core/esm/components/RingProgress/RingProgress.mjs","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\svp\\frontend\\medicine\\node_modules\\@mantine\\core\\esm\\components\\ScrollArea\\ScrollArea.mjs":{"id":"(app-pages-browser)/./node_modules/@mantine/core/esm/components/ScrollArea/ScrollArea.mjs","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\svp\\frontend\\medicine\\node_modules\\@mantine\\core\\esm\\components\\SegmentedControl\\SegmentedControl.mjs":{"id":"(app-pages-browser)/./node_modules/@mantine/core/esm/components/SegmentedControl/SegmentedControl.mjs","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\svp\\frontend\\medicine\\node_modules\\@mantine\\core\\esm\\components\\Select\\Select.mjs":{"id":"(app-pages-browser)/./node_modules/@mantine/core/esm/components/Select/Select.mjs","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\svp\\frontend\\medicine\\node_modules\\@mantine\\core\\esm\\components\\SemiCircleProgress\\SemiCircleProgress.mjs":{"id":"(app-pages-browser)/./node_modules/@mantine/core/esm/components/SemiCircleProgress/SemiCircleProgress.mjs","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\svp\\frontend\\medicine\\node_modules\\@mantine\\core\\esm\\components\\SimpleGrid\\SimpleGrid.mjs":{"id":"(app-pages-browser)/./node_modules/@mantine/core/esm/components/SimpleGrid/SimpleGrid.mjs","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\svp\\frontend\\medicine\\node_modules\\@mantine\\core\\esm\\components\\Skeleton\\Skeleton.mjs":{"id":"(app-pages-browser)/./node_modules/@mantine/core/esm/components/Skeleton/Skeleton.mjs","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\svp\\frontend\\medicine\\node_modules\\@mantine\\core\\esm\\components\\Slider\\RangeSlider\\RangeSlider.mjs":{"id":"(app-pages-browser)/./node_modules/@mantine/core/esm/components/Slider/RangeSlider/RangeSlider.mjs","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\svp\\frontend\\medicine\\node_modules\\@mantine\\core\\esm\\components\\Slider\\Slider\\Slider.mjs":{"id":"(app-pages-browser)/./node_modules/@mantine/core/esm/components/Slider/Slider/Slider.mjs","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\svp\\frontend\\medicine\\node_modules\\@mantine\\core\\esm\\components\\Space\\Space.mjs":{"id":"(app-pages-browser)/./node_modules/@mantine/core/esm/components/Space/Space.mjs","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\svp\\frontend\\medicine\\node_modules\\@mantine\\core\\esm\\components\\Spoiler\\Spoiler.mjs":{"id":"(app-pages-browser)/./node_modules/@mantine/core/esm/components/Spoiler/Spoiler.mjs","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\svp\\frontend\\medicine\\node_modules\\@mantine\\core\\esm\\components\\Stack\\Stack.mjs":{"id":"(app-pages-browser)/./node_modules/@mantine/core/esm/components/Stack/Stack.mjs","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\svp\\frontend\\medicine\\node_modules\\@mantine\\core\\esm\\components\\Stepper\\Stepper.mjs":{"id":"(app-pages-browser)/./node_modules/@mantine/core/esm/components/Stepper/Stepper.mjs","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\svp\\frontend\\medicine\\node_modules\\@mantine\\core\\esm\\components\\Stepper\\StepperCompleted\\StepperCompleted.mjs":{"id":"(app-pages-browser)/./node_modules/@mantine/core/esm/components/Stepper/StepperCompleted/StepperCompleted.mjs","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\svp\\frontend\\medicine\\node_modules\\@mantine\\core\\esm\\components\\Stepper\\StepperStep\\StepperStep.mjs":{"id":"(app-pages-browser)/./node_modules/@mantine/core/esm/components/Stepper/StepperStep/StepperStep.mjs","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\svp\\frontend\\medicine\\node_modules\\@mantine\\core\\esm\\components\\Switch\\Switch.mjs":{"id":"(app-pages-browser)/./node_modules/@mantine/core/esm/components/Switch/Switch.mjs","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\svp\\frontend\\medicine\\node_modules\\@mantine\\core\\esm\\components\\Switch\\SwitchGroup\\SwitchGroup.mjs":{"id":"(app-pages-browser)/./node_modules/@mantine/core/esm/components/Switch/SwitchGroup/SwitchGroup.mjs","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\svp\\frontend\\medicine\\node_modules\\@mantine\\core\\esm\\components\\Table\\Table.components.mjs":{"id":"(app-pages-browser)/./node_modules/@mantine/core/esm/components/Table/Table.components.mjs","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\svp\\frontend\\medicine\\node_modules\\@mantine\\core\\esm\\components\\Table\\Table.mjs":{"id":"(app-pages-browser)/./node_modules/@mantine/core/esm/components/Table/Table.mjs","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\svp\\frontend\\medicine\\node_modules\\@mantine\\core\\esm\\components\\Table\\TableScrollContainer.mjs":{"id":"(app-pages-browser)/./node_modules/@mantine/core/esm/components/Table/TableScrollContainer.mjs","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\svp\\frontend\\medicine\\node_modules\\@mantine\\core\\esm\\components\\TableOfContents\\TableOfContents.mjs":{"id":"(app-pages-browser)/./node_modules/@mantine/core/esm/components/TableOfContents/TableOfContents.mjs","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\svp\\frontend\\medicine\\node_modules\\@mantine\\core\\esm\\components\\Tabs\\Tabs.mjs":{"id":"(app-pages-browser)/./node_modules/@mantine/core/esm/components/Tabs/Tabs.mjs","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\svp\\frontend\\medicine\\node_modules\\@mantine\\core\\esm\\components\\Tabs\\TabsList\\TabsList.mjs":{"id":"(app-pages-browser)/./node_modules/@mantine/core/esm/components/Tabs/TabsList/TabsList.mjs","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\svp\\frontend\\medicine\\node_modules\\@mantine\\core\\esm\\components\\Tabs\\TabsPanel\\TabsPanel.mjs":{"id":"(app-pages-browser)/./node_modules/@mantine/core/esm/components/Tabs/TabsPanel/TabsPanel.mjs","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\svp\\frontend\\medicine\\node_modules\\@mantine\\core\\esm\\components\\Tabs\\TabsTab\\TabsTab.mjs":{"id":"(app-pages-browser)/./node_modules/@mantine/core/esm/components/Tabs/TabsTab/TabsTab.mjs","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\svp\\frontend\\medicine\\node_modules\\@mantine\\core\\esm\\components\\TagsInput\\TagsInput.mjs":{"id":"(app-pages-browser)/./node_modules/@mantine/core/esm/components/TagsInput/TagsInput.mjs","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\svp\\frontend\\medicine\\node_modules\\@mantine\\core\\esm\\components\\Text\\Text.mjs":{"id":"(app-pages-browser)/./node_modules/@mantine/core/esm/components/Text/Text.mjs","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\svp\\frontend\\medicine\\node_modules\\@mantine\\core\\esm\\components\\Textarea\\Textarea.mjs":{"id":"(app-pages-browser)/./node_modules/@mantine/core/esm/components/Textarea/Textarea.mjs","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\svp\\frontend\\medicine\\node_modules\\@mantine\\core\\esm\\components\\TextInput\\TextInput.mjs":{"id":"(app-pages-browser)/./node_modules/@mantine/core/esm/components/TextInput/TextInput.mjs","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\svp\\frontend\\medicine\\node_modules\\@mantine\\core\\esm\\components\\ThemeIcon\\ThemeIcon.mjs":{"id":"(app-pages-browser)/./node_modules/@mantine/core/esm/components/ThemeIcon/ThemeIcon.mjs","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\svp\\frontend\\medicine\\node_modules\\@mantine\\core\\esm\\components\\Timeline\\Timeline.mjs":{"id":"(app-pages-browser)/./node_modules/@mantine/core/esm/components/Timeline/Timeline.mjs","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\svp\\frontend\\medicine\\node_modules\\@mantine\\core\\esm\\components\\Timeline\\TimelineItem\\TimelineItem.mjs":{"id":"(app-pages-browser)/./node_modules/@mantine/core/esm/components/Timeline/TimelineItem/TimelineItem.mjs","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\svp\\frontend\\medicine\\node_modules\\@mantine\\core\\esm\\components\\Title\\Title.mjs":{"id":"(app-pages-browser)/./node_modules/@mantine/core/esm/components/Title/Title.mjs","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\svp\\frontend\\medicine\\node_modules\\@mantine\\core\\esm\\components\\Tooltip\\Tooltip.mjs":{"id":"(app-pages-browser)/./node_modules/@mantine/core/esm/components/Tooltip/Tooltip.mjs","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\svp\\frontend\\medicine\\node_modules\\@mantine\\core\\esm\\components\\Tooltip\\TooltipFloating\\TooltipFloating.mjs":{"id":"(app-pages-browser)/./node_modules/@mantine/core/esm/components/Tooltip/TooltipFloating/TooltipFloating.mjs","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\svp\\frontend\\medicine\\node_modules\\@mantine\\core\\esm\\components\\Tooltip\\TooltipGroup\\TooltipGroup.mjs":{"id":"(app-pages-browser)/./node_modules/@mantine/core/esm/components/Tooltip/TooltipGroup/TooltipGroup.mjs","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\svp\\frontend\\medicine\\node_modules\\@mantine\\core\\esm\\components\\Transition\\get-transition-props\\get-transition-props.mjs":{"id":"(app-pages-browser)/./node_modules/@mantine/core/esm/components/Transition/get-transition-props/get-transition-props.mjs","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\svp\\frontend\\medicine\\node_modules\\@mantine\\core\\esm\\components\\Transition\\Transition.mjs":{"id":"(app-pages-browser)/./node_modules/@mantine/core/esm/components/Transition/Transition.mjs","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\svp\\frontend\\medicine\\node_modules\\@mantine\\core\\esm\\components\\Transition\\transitions.mjs":{"id":"(app-pages-browser)/./node_modules/@mantine/core/esm/components/Transition/transitions.mjs","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\svp\\frontend\\medicine\\node_modules\\@mantine\\core\\esm\\components\\Tree\\Tree.mjs":{"id":"(app-pages-browser)/./node_modules/@mantine/core/esm/components/Tree/Tree.mjs","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\svp\\frontend\\medicine\\node_modules\\@mantine\\core\\esm\\components\\Tree\\use-tree.mjs":{"id":"(app-pages-browser)/./node_modules/@mantine/core/esm/components/Tree/use-tree.mjs","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\svp\\frontend\\medicine\\node_modules\\@mantine\\core\\esm\\components\\TypographyStylesProvider\\TypographyStylesProvider.mjs":{"id":"(app-pages-browser)/./node_modules/@mantine/core/esm/components/TypographyStylesProvider/TypographyStylesProvider.mjs","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\svp\\frontend\\medicine\\node_modules\\@mantine\\core\\esm\\components\\UnstyledButton\\UnstyledButton.mjs":{"id":"(app-pages-browser)/./node_modules/@mantine/core/esm/components/UnstyledButton/UnstyledButton.mjs","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\svp\\frontend\\medicine\\node_modules\\@mantine\\core\\esm\\components\\VisuallyHidden\\VisuallyHidden.mjs":{"id":"(app-pages-browser)/./node_modules/@mantine/core/esm/components/VisuallyHidden/VisuallyHidden.mjs","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\svp\\frontend\\medicine\\node_modules\\@mantine\\core\\esm\\core\\Box\\Box.mjs":{"id":"(app-pages-browser)/./node_modules/@mantine/core/esm/core/Box/Box.mjs","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\svp\\frontend\\medicine\\node_modules\\@mantine\\core\\esm\\core\\Box\\get-style-object\\get-style-object.mjs":{"id":"(app-pages-browser)/./node_modules/@mantine/core/esm/core/Box/get-style-object/get-style-object.mjs","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\svp\\frontend\\medicine\\node_modules\\@mantine\\core\\esm\\core\\Box\\style-props\\extract-style-props\\extract-style-props.mjs":{"id":"(app-pages-browser)/./node_modules/@mantine/core/esm/core/Box/style-props/extract-style-props/extract-style-props.mjs","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\svp\\frontend\\medicine\\node_modules\\@mantine\\core\\esm\\core\\Box\\style-props\\parse-style-props\\parse-style-props.mjs":{"id":"(app-pages-browser)/./node_modules/@mantine/core/esm/core/Box/style-props/parse-style-props/parse-style-props.mjs","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\svp\\frontend\\medicine\\node_modules\\@mantine\\core\\esm\\core\\Box\\style-props\\style-props-data.mjs":{"id":"(app-pages-browser)/./node_modules/@mantine/core/esm/core/Box/style-props/style-props-data.mjs","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\svp\\frontend\\medicine\\node_modules\\@mantine\\core\\esm\\core\\Box\\use-random-classname\\use-random-classname.mjs":{"id":"(app-pages-browser)/./node_modules/@mantine/core/esm/core/Box/use-random-classname/use-random-classname.mjs","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\svp\\frontend\\medicine\\node_modules\\@mantine\\core\\esm\\core\\DirectionProvider\\DirectionProvider.mjs":{"id":"(app-pages-browser)/./node_modules/@mantine/core/esm/core/DirectionProvider/DirectionProvider.mjs","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\svp\\frontend\\medicine\\node_modules\\@mantine\\core\\esm\\core\\factory\\factory.mjs":{"id":"(app-pages-browser)/./node_modules/@mantine/core/esm/core/factory/factory.mjs","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\svp\\frontend\\medicine\\node_modules\\@mantine\\core\\esm\\core\\factory\\polymorphic-factory.mjs":{"id":"(app-pages-browser)/./node_modules/@mantine/core/esm/core/factory/polymorphic-factory.mjs","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\svp\\frontend\\medicine\\node_modules\\@mantine\\core\\esm\\core\\InlineStyles\\InlineStyles.mjs":{"id":"(app-pages-browser)/./node_modules/@mantine/core/esm/core/InlineStyles/InlineStyles.mjs","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\svp\\frontend\\medicine\\node_modules\\@mantine\\core\\esm\\core\\InlineStyles\\styles-to-string\\styles-to-string.mjs":{"id":"(app-pages-browser)/./node_modules/@mantine/core/esm/core/InlineStyles/styles-to-string/styles-to-string.mjs","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\svp\\frontend\\medicine\\node_modules\\@mantine\\core\\esm\\core\\MantineProvider\\color-functions\\default-variant-colors-resolver\\default-variant-colors-resolver.mjs":{"id":"(app-pages-browser)/./node_modules/@mantine/core/esm/core/MantineProvider/color-functions/default-variant-colors-resolver/default-variant-colors-resolver.mjs","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\svp\\frontend\\medicine\\node_modules\\@mantine\\core\\esm\\core\\MantineProvider\\color-functions\\get-auto-contrast-value\\get-auto-contrast-value.mjs":{"id":"(app-pages-browser)/./node_modules/@mantine/core/esm/core/MantineProvider/color-functions/get-auto-contrast-value/get-auto-contrast-value.mjs","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\svp\\frontend\\medicine\\node_modules\\@mantine\\core\\esm\\core\\MantineProvider\\color-functions\\get-contrast-color\\get-contrast-color.mjs":{"id":"(app-pages-browser)/./node_modules/@mantine/core/esm/core/MantineProvider/color-functions/get-contrast-color/get-contrast-color.mjs","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\svp\\frontend\\medicine\\node_modules\\@mantine\\core\\esm\\core\\MantineProvider\\color-functions\\get-gradient\\get-gradient.mjs":{"id":"(app-pages-browser)/./node_modules/@mantine/core/esm/core/MantineProvider/color-functions/get-gradient/get-gradient.mjs","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\svp\\frontend\\medicine\\node_modules\\@mantine\\core\\esm\\core\\MantineProvider\\color-functions\\get-primary-shade\\get-primary-shade.mjs":{"id":"(app-pages-browser)/./node_modules/@mantine/core/esm/core/MantineProvider/color-functions/get-primary-shade/get-primary-shade.mjs","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\svp\\frontend\\medicine\\node_modules\\@mantine\\core\\esm\\core\\MantineProvider\\color-functions\\get-theme-color\\get-theme-color.mjs":{"id":"(app-pages-browser)/./node_modules/@mantine/core/esm/core/MantineProvider/color-functions/get-theme-color/get-theme-color.mjs","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\svp\\frontend\\medicine\\node_modules\\@mantine\\core\\esm\\core\\MantineProvider\\color-functions\\parse-theme-color\\parse-theme-color.mjs":{"id":"(app-pages-browser)/./node_modules/@mantine/core/esm/core/MantineProvider/color-functions/parse-theme-color/parse-theme-color.mjs","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\svp\\frontend\\medicine\\node_modules\\@mantine\\core\\esm\\core\\MantineProvider\\color-scheme-managers\\is-mantine-color-scheme.mjs":{"id":"(app-pages-browser)/./node_modules/@mantine/core/esm/core/MantineProvider/color-scheme-managers/is-mantine-color-scheme.mjs","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\svp\\frontend\\medicine\\node_modules\\@mantine\\core\\esm\\core\\MantineProvider\\color-scheme-managers\\local-storage-manager.mjs":{"id":"(app-pages-browser)/./node_modules/@mantine/core/esm/core/MantineProvider/color-scheme-managers/local-storage-manager.mjs","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\svp\\frontend\\medicine\\node_modules\\@mantine\\core\\esm\\core\\MantineProvider\\ColorSchemeScript\\ColorSchemeScript.mjs":{"id":"(app-pages-browser)/./node_modules/@mantine/core/esm/core/MantineProvider/ColorSchemeScript/ColorSchemeScript.mjs","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\svp\\frontend\\medicine\\node_modules\\@mantine\\core\\esm\\core\\MantineProvider\\convert-css-variables\\convert-css-variables.mjs":{"id":"(app-pages-browser)/./node_modules/@mantine/core/esm/core/MantineProvider/convert-css-variables/convert-css-variables.mjs","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\svp\\frontend\\medicine\\node_modules\\@mantine\\core\\esm\\core\\MantineProvider\\Mantine.context.mjs":{"id":"(app-pages-browser)/./node_modules/@mantine/core/esm/core/MantineProvider/Mantine.context.mjs","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\svp\\frontend\\medicine\\node_modules\\@mantine\\core\\esm\\core\\MantineProvider\\MantineCssVariables\\default-css-variables-resolver.mjs":{"id":"(app-pages-browser)/./node_modules/@mantine/core/esm/core/MantineProvider/MantineCssVariables/default-css-variables-resolver.mjs","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\svp\\frontend\\medicine\\node_modules\\@mantine\\core\\esm\\core\\MantineProvider\\MantineCssVariables\\get-css-color-variables.mjs":{"id":"(app-pages-browser)/./node_modules/@mantine/core/esm/core/MantineProvider/MantineCssVariables/get-css-color-variables.mjs","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\svp\\frontend\\medicine\\node_modules\\@mantine\\core\\esm\\core\\MantineProvider\\MantineCssVariables\\MantineCssVariables.mjs":{"id":"(app-pages-browser)/./node_modules/@mantine/core/esm/core/MantineProvider/MantineCssVariables/MantineCssVariables.mjs","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\svp\\frontend\\medicine\\node_modules\\@mantine\\core\\esm\\core\\MantineProvider\\MantineProvider.mjs":{"id":"(app-pages-browser)/./node_modules/@mantine/core/esm/core/MantineProvider/MantineProvider.mjs","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\svp\\frontend\\medicine\\node_modules\\@mantine\\core\\esm\\core\\MantineProvider\\MantineThemeProvider\\MantineThemeProvider.mjs":{"id":"(app-pages-browser)/./node_modules/@mantine/core/esm/core/MantineProvider/MantineThemeProvider/MantineThemeProvider.mjs","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\svp\\frontend\\medicine\\node_modules\\@mantine\\core\\esm\\core\\MantineProvider\\merge-theme-overrides\\merge-theme-overrides.mjs":{"id":"(app-pages-browser)/./node_modules/@mantine/core/esm/core/MantineProvider/merge-theme-overrides/merge-theme-overrides.mjs","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\svp\\frontend\\medicine\\node_modules\\@mantine\\core\\esm\\core\\MantineProvider\\use-mantine-color-scheme\\use-computed-color-scheme.mjs":{"id":"(app-pages-browser)/./node_modules/@mantine/core/esm/core/MantineProvider/use-mantine-color-scheme/use-computed-color-scheme.mjs","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\svp\\frontend\\medicine\\node_modules\\@mantine\\core\\esm\\core\\MantineProvider\\use-mantine-color-scheme\\use-mantine-color-scheme.mjs":{"id":"(app-pages-browser)/./node_modules/@mantine/core/esm/core/MantineProvider/use-mantine-color-scheme/use-mantine-color-scheme.mjs","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\svp\\frontend\\medicine\\node_modules\\@mantine\\core\\esm\\core\\MantineProvider\\use-mantine-color-scheme\\use-provider-color-scheme.mjs":{"id":"(app-pages-browser)/./node_modules/@mantine/core/esm/core/MantineProvider/use-mantine-color-scheme/use-provider-color-scheme.mjs","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\svp\\frontend\\medicine\\node_modules\\@mantine\\core\\esm\\core\\MantineProvider\\use-matches\\use-matches.mjs":{"id":"(app-pages-browser)/./node_modules/@mantine/core/esm/core/MantineProvider/use-matches/use-matches.mjs","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\svp\\frontend\\medicine\\node_modules\\@mantine\\core\\esm\\core\\MantineProvider\\use-props\\use-props.mjs":{"id":"(app-pages-browser)/./node_modules/@mantine/core/esm/core/MantineProvider/use-props/use-props.mjs","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\svp\\frontend\\medicine\\node_modules\\@mantine\\core\\esm\\core\\styles-api\\create-vars-resolver\\create-vars-resolver.mjs":{"id":"(app-pages-browser)/./node_modules/@mantine/core/esm/core/styles-api/create-vars-resolver/create-vars-resolver.mjs","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\svp\\frontend\\medicine\\node_modules\\@mantine\\core\\esm\\core\\styles-api\\use-resolved-styles-api\\use-resolved-styles-api.mjs":{"id":"(app-pages-browser)/./node_modules/@mantine/core/esm/core/styles-api/use-resolved-styles-api/use-resolved-styles-api.mjs","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\svp\\frontend\\medicine\\node_modules\\@mantine\\core\\esm\\core\\styles-api\\use-styles\\get-class-name\\get-global-class-names\\get-global-class-names.mjs":{"id":"(app-pages-browser)/./node_modules/@mantine/core/esm/core/styles-api/use-styles/get-class-name/get-global-class-names/get-global-class-names.mjs","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\svp\\frontend\\medicine\\node_modules\\@mantine\\core\\esm\\core\\styles-api\\use-styles\\get-class-name\\resolve-class-names\\resolve-class-names.mjs":{"id":"(app-pages-browser)/./node_modules/@mantine/core/esm/core/styles-api/use-styles/get-class-name/resolve-class-names/resolve-class-names.mjs","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\svp\\frontend\\medicine\\node_modules\\@mantine\\core\\esm\\core\\styles-api\\use-styles\\get-style\\resolve-styles\\resolve-styles.mjs":{"id":"(app-pages-browser)/./node_modules/@mantine/core/esm/core/styles-api/use-styles/get-style/resolve-styles/resolve-styles.mjs","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\svp\\frontend\\medicine\\node_modules\\@mantine\\core\\esm\\core\\styles-api\\use-styles\\use-styles.mjs":{"id":"(app-pages-browser)/./node_modules/@mantine/core/esm/core/styles-api/use-styles/use-styles.mjs","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\svp\\frontend\\medicine\\node_modules\\@mantine\\core\\esm\\core\\utils\\camel-to-kebab-case\\camel-to-kebab-case.mjs":{"id":"(app-pages-browser)/./node_modules/@mantine/core/esm/core/utils/camel-to-kebab-case/camel-to-kebab-case.mjs","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\svp\\frontend\\medicine\\node_modules\\@mantine\\core\\esm\\core\\utils\\close-on-escape\\close-on-escape.mjs":{"id":"(app-pages-browser)/./node_modules/@mantine/core/esm/core/utils/close-on-escape/close-on-escape.mjs","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\svp\\frontend\\medicine\\node_modules\\@mantine\\core\\esm\\core\\utils\\create-event-handler\\create-event-handler.mjs":{"id":"(app-pages-browser)/./node_modules/@mantine/core/esm/core/utils/create-event-handler/create-event-handler.mjs","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\svp\\frontend\\medicine\\node_modules\\@mantine\\core\\esm\\core\\utils\\create-optional-context\\create-optional-context.mjs":{"id":"(app-pages-browser)/./node_modules/@mantine/core/esm/core/utils/create-optional-context/create-optional-context.mjs","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\svp\\frontend\\medicine\\node_modules\\@mantine\\core\\esm\\core\\utils\\create-safe-context\\create-safe-context.mjs":{"id":"(app-pages-browser)/./node_modules/@mantine/core/esm/core/utils/create-safe-context/create-safe-context.mjs","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\svp\\frontend\\medicine\\node_modules\\@mantine\\core\\esm\\core\\utils\\create-scoped-keydown-handler\\create-scoped-keydown-handler.mjs":{"id":"(app-pages-browser)/./node_modules/@mantine/core/esm/core/utils/create-scoped-keydown-handler/create-scoped-keydown-handler.mjs","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\svp\\frontend\\medicine\\node_modules\\@mantine\\core\\esm\\core\\utils\\create-use-external-events\\create-use-external-events.mjs":{"id":"(app-pages-browser)/./node_modules/@mantine/core/esm/core/utils/create-use-external-events/create-use-external-events.mjs","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\svp\\frontend\\medicine\\node_modules\\@mantine\\core\\esm\\core\\utils\\filter-props\\filter-props.mjs":{"id":"(app-pages-browser)/./node_modules/@mantine/core/esm/core/utils/filter-props/filter-props.mjs","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\svp\\frontend\\medicine\\node_modules\\@mantine\\core\\esm\\core\\utils\\find-closest-number\\find-closest-number.mjs":{"id":"(app-pages-browser)/./node_modules/@mantine/core/esm/core/utils/find-closest-number/find-closest-number.mjs","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\svp\\frontend\\medicine\\node_modules\\@mantine\\core\\esm\\core\\utils\\find-element-ancestor\\find-element-ancestor.mjs":{"id":"(app-pages-browser)/./node_modules/@mantine/core/esm/core/utils/find-element-ancestor/find-element-ancestor.mjs","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\svp\\frontend\\medicine\\node_modules\\@mantine\\core\\esm\\core\\utils\\get-base-value\\get-base-value.mjs":{"id":"(app-pages-browser)/./node_modules/@mantine/core/esm/core/utils/get-base-value/get-base-value.mjs","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\svp\\frontend\\medicine\\node_modules\\@mantine\\core\\esm\\core\\utils\\get-breakpoint-value\\get-breakpoint-value.mjs":{"id":"(app-pages-browser)/./node_modules/@mantine/core/esm/core/utils/get-breakpoint-value/get-breakpoint-value.mjs","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\svp\\frontend\\medicine\\node_modules\\@mantine\\core\\esm\\core\\utils\\get-context-item-index\\get-context-item-index.mjs":{"id":"(app-pages-browser)/./node_modules/@mantine/core/esm/core/utils/get-context-item-index/get-context-item-index.mjs","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\svp\\frontend\\medicine\\node_modules\\@mantine\\core\\esm\\core\\utils\\get-default-z-index\\get-default-z-index.mjs":{"id":"(app-pages-browser)/./node_modules/@mantine/core/esm/core/utils/get-default-z-index/get-default-z-index.mjs","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\svp\\frontend\\medicine\\node_modules\\@mantine\\core\\esm\\core\\utils\\get-env\\get-env.mjs":{"id":"(app-pages-browser)/./node_modules/@mantine/core/esm/core/utils/get-env/get-env.mjs","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\svp\\frontend\\medicine\\node_modules\\@mantine\\core\\esm\\core\\utils\\get-ref-prop\\get-ref-prop.mjs":{"id":"(app-pages-browser)/./node_modules/@mantine/core/esm/core/utils/get-ref-prop/get-ref-prop.mjs","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\svp\\frontend\\medicine\\node_modules\\@mantine\\core\\esm\\core\\utils\\get-safe-id\\get-safe-id.mjs":{"id":"(app-pages-browser)/./node_modules/@mantine/core/esm/core/utils/get-safe-id/get-safe-id.mjs","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\svp\\frontend\\medicine\\node_modules\\@mantine\\core\\esm\\core\\utils\\get-size\\get-size.mjs":{"id":"(app-pages-browser)/./node_modules/@mantine/core/esm/core/utils/get-size/get-size.mjs","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\svp\\frontend\\medicine\\node_modules\\@mantine\\core\\esm\\core\\utils\\get-sorted-breakpoints\\get-sorted-breakpoints.mjs":{"id":"(app-pages-browser)/./node_modules/@mantine/core/esm/core/utils/get-sorted-breakpoints/get-sorted-breakpoints.mjs","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\svp\\frontend\\medicine\\node_modules\\@mantine\\core\\esm\\core\\utils\\is-element\\is-element.mjs":{"id":"(app-pages-browser)/./node_modules/@mantine/core/esm/core/utils/is-element/is-element.mjs","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\svp\\frontend\\medicine\\node_modules\\@mantine\\core\\esm\\core\\utils\\is-number-like\\is-number-like.mjs":{"id":"(app-pages-browser)/./node_modules/@mantine/core/esm/core/utils/is-number-like/is-number-like.mjs","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\svp\\frontend\\medicine\\node_modules\\@mantine\\core\\esm\\core\\utils\\keys\\keys.mjs":{"id":"(app-pages-browser)/./node_modules/@mantine/core/esm/core/utils/keys/keys.mjs","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\svp\\frontend\\medicine\\node_modules\\@mantine\\core\\esm\\core\\utils\\memoize\\memoize.mjs":{"id":"(app-pages-browser)/./node_modules/@mantine/core/esm/core/utils/memoize/memoize.mjs","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\svp\\frontend\\medicine\\node_modules\\@mantine\\core\\esm\\core\\utils\\noop\\noop.mjs":{"id":"(app-pages-browser)/./node_modules/@mantine/core/esm/core/utils/noop/noop.mjs","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\svp\\frontend\\medicine\\node_modules\\@mantine\\core\\esm\\core\\utils\\use-hovered\\use-hovered.mjs":{"id":"(app-pages-browser)/./node_modules/@mantine/core/esm/core/utils/use-hovered/use-hovered.mjs","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\svp\\frontend\\medicine\\node_modules\\@mantine\\core\\styles.css":{"id":"(app-pages-browser)/./node_modules/@mantine/core/styles.css","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\svp\\frontend\\medicine\\node_modules\\@mantine\\hooks\\esm\\use-callback-ref\\use-callback-ref.mjs":{"id":"(app-pages-browser)/./node_modules/@mantine/hooks/esm/use-callback-ref/use-callback-ref.mjs","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\svp\\frontend\\medicine\\node_modules\\@mantine\\hooks\\esm\\use-click-outside\\use-click-outside.mjs":{"id":"(app-pages-browser)/./node_modules/@mantine/hooks/esm/use-click-outside/use-click-outside.mjs","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\svp\\frontend\\medicine\\node_modules\\@mantine\\hooks\\esm\\use-clipboard\\use-clipboard.mjs":{"id":"(app-pages-browser)/./node_modules/@mantine/hooks/esm/use-clipboard/use-clipboard.mjs","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\svp\\frontend\\medicine\\node_modules\\@mantine\\hooks\\esm\\use-color-scheme\\use-color-scheme.mjs":{"id":"(app-pages-browser)/./node_modules/@mantine/hooks/esm/use-color-scheme/use-color-scheme.mjs","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\svp\\frontend\\medicine\\node_modules\\@mantine\\hooks\\esm\\use-counter\\use-counter.mjs":{"id":"(app-pages-browser)/./node_modules/@mantine/hooks/esm/use-counter/use-counter.mjs","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\svp\\frontend\\medicine\\node_modules\\@mantine\\hooks\\esm\\use-debounced-callback\\use-debounced-callback.mjs":{"id":"(app-pages-browser)/./node_modules/@mantine/hooks/esm/use-debounced-callback/use-debounced-callback.mjs","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\svp\\frontend\\medicine\\node_modules\\@mantine\\hooks\\esm\\use-debounced-state\\use-debounced-state.mjs":{"id":"(app-pages-browser)/./node_modules/@mantine/hooks/esm/use-debounced-state/use-debounced-state.mjs","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\svp\\frontend\\medicine\\node_modules\\@mantine\\hooks\\esm\\use-debounced-value\\use-debounced-value.mjs":{"id":"(app-pages-browser)/./node_modules/@mantine/hooks/esm/use-debounced-value/use-debounced-value.mjs","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\svp\\frontend\\medicine\\node_modules\\@mantine\\hooks\\esm\\use-did-update\\use-did-update.mjs":{"id":"(app-pages-browser)/./node_modules/@mantine/hooks/esm/use-did-update/use-did-update.mjs","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\svp\\frontend\\medicine\\node_modules\\@mantine\\hooks\\esm\\use-disclosure\\use-disclosure.mjs":{"id":"(app-pages-browser)/./node_modules/@mantine/hooks/esm/use-disclosure/use-disclosure.mjs","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\svp\\frontend\\medicine\\node_modules\\@mantine\\hooks\\esm\\use-document-title\\use-document-title.mjs":{"id":"(app-pages-browser)/./node_modules/@mantine/hooks/esm/use-document-title/use-document-title.mjs","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\svp\\frontend\\medicine\\node_modules\\@mantine\\hooks\\esm\\use-document-visibility\\use-document-visibility.mjs":{"id":"(app-pages-browser)/./node_modules/@mantine/hooks/esm/use-document-visibility/use-document-visibility.mjs","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\svp\\frontend\\medicine\\node_modules\\@mantine\\hooks\\esm\\use-event-listener\\use-event-listener.mjs":{"id":"(app-pages-browser)/./node_modules/@mantine/hooks/esm/use-event-listener/use-event-listener.mjs","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\svp\\frontend\\medicine\\node_modules\\@mantine\\hooks\\esm\\use-eye-dropper\\use-eye-dropper.mjs":{"id":"(app-pages-browser)/./node_modules/@mantine/hooks/esm/use-eye-dropper/use-eye-dropper.mjs","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\svp\\frontend\\medicine\\node_modules\\@mantine\\hooks\\esm\\use-favicon\\use-favicon.mjs":{"id":"(app-pages-browser)/./node_modules/@mantine/hooks/esm/use-favicon/use-favicon.mjs","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\svp\\frontend\\medicine\\node_modules\\@mantine\\hooks\\esm\\use-fetch\\use-fetch.mjs":{"id":"(app-pages-browser)/./node_modules/@mantine/hooks/esm/use-fetch/use-fetch.mjs","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\svp\\frontend\\medicine\\node_modules\\@mantine\\hooks\\esm\\use-file-dialog\\use-file-dialog.mjs":{"id":"(app-pages-browser)/./node_modules/@mantine/hooks/esm/use-file-dialog/use-file-dialog.mjs","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\svp\\frontend\\medicine\\node_modules\\@mantine\\hooks\\esm\\use-focus-return\\use-focus-return.mjs":{"id":"(app-pages-browser)/./node_modules/@mantine/hooks/esm/use-focus-return/use-focus-return.mjs","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\svp\\frontend\\medicine\\node_modules\\@mantine\\hooks\\esm\\use-focus-trap\\use-focus-trap.mjs":{"id":"(app-pages-browser)/./node_modules/@mantine/hooks/esm/use-focus-trap/use-focus-trap.mjs","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\svp\\frontend\\medicine\\node_modules\\@mantine\\hooks\\esm\\use-focus-within\\use-focus-within.mjs":{"id":"(app-pages-browser)/./node_modules/@mantine/hooks/esm/use-focus-within/use-focus-within.mjs","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\svp\\frontend\\medicine\\node_modules\\@mantine\\hooks\\esm\\use-force-update\\use-force-update.mjs":{"id":"(app-pages-browser)/./node_modules/@mantine/hooks/esm/use-force-update/use-force-update.mjs","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\svp\\frontend\\medicine\\node_modules\\@mantine\\hooks\\esm\\use-fullscreen\\use-fullscreen.mjs":{"id":"(app-pages-browser)/./node_modules/@mantine/hooks/esm/use-fullscreen/use-fullscreen.mjs","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\svp\\frontend\\medicine\\node_modules\\@mantine\\hooks\\esm\\use-hash\\use-hash.mjs":{"id":"(app-pages-browser)/./node_modules/@mantine/hooks/esm/use-hash/use-hash.mjs","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\svp\\frontend\\medicine\\node_modules\\@mantine\\hooks\\esm\\use-headroom\\use-headroom.mjs":{"id":"(app-pages-browser)/./node_modules/@mantine/hooks/esm/use-headroom/use-headroom.mjs","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\svp\\frontend\\medicine\\node_modules\\@mantine\\hooks\\esm\\use-hotkeys\\parse-hotkey.mjs":{"id":"(app-pages-browser)/./node_modules/@mantine/hooks/esm/use-hotkeys/parse-hotkey.mjs","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\svp\\frontend\\medicine\\node_modules\\@mantine\\hooks\\esm\\use-hotkeys\\use-hotkeys.mjs":{"id":"(app-pages-browser)/./node_modules/@mantine/hooks/esm/use-hotkeys/use-hotkeys.mjs","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\svp\\frontend\\medicine\\node_modules\\@mantine\\hooks\\esm\\use-hover\\use-hover.mjs":{"id":"(app-pages-browser)/./node_modules/@mantine/hooks/esm/use-hover/use-hover.mjs","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\svp\\frontend\\medicine\\node_modules\\@mantine\\hooks\\esm\\use-id\\use-id.mjs":{"id":"(app-pages-browser)/./node_modules/@mantine/hooks/esm/use-id/use-id.mjs","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\svp\\frontend\\medicine\\node_modules\\@mantine\\hooks\\esm\\use-idle\\use-idle.mjs":{"id":"(app-pages-browser)/./node_modules/@mantine/hooks/esm/use-idle/use-idle.mjs","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\svp\\frontend\\medicine\\node_modules\\@mantine\\hooks\\esm\\use-in-viewport\\use-in-viewport.mjs":{"id":"(app-pages-browser)/./node_modules/@mantine/hooks/esm/use-in-viewport/use-in-viewport.mjs","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\svp\\frontend\\medicine\\node_modules\\@mantine\\hooks\\esm\\use-input-state\\use-input-state.mjs":{"id":"(app-pages-browser)/./node_modules/@mantine/hooks/esm/use-input-state/use-input-state.mjs","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\svp\\frontend\\medicine\\node_modules\\@mantine\\hooks\\esm\\use-intersection\\use-intersection.mjs":{"id":"(app-pages-browser)/./node_modules/@mantine/hooks/esm/use-intersection/use-intersection.mjs","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\svp\\frontend\\medicine\\node_modules\\@mantine\\hooks\\esm\\use-interval\\use-interval.mjs":{"id":"(app-pages-browser)/./node_modules/@mantine/hooks/esm/use-interval/use-interval.mjs","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\svp\\frontend\\medicine\\node_modules\\@mantine\\hooks\\esm\\use-is-first-render\\use-is-first-render.mjs":{"id":"(app-pages-browser)/./node_modules/@mantine/hooks/esm/use-is-first-render/use-is-first-render.mjs","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\svp\\frontend\\medicine\\node_modules\\@mantine\\hooks\\esm\\use-isomorphic-effect\\use-isomorphic-effect.mjs":{"id":"(app-pages-browser)/./node_modules/@mantine/hooks/esm/use-isomorphic-effect/use-isomorphic-effect.mjs","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\svp\\frontend\\medicine\\node_modules\\@mantine\\hooks\\esm\\use-list-state\\use-list-state.mjs":{"id":"(app-pages-browser)/./node_modules/@mantine/hooks/esm/use-list-state/use-list-state.mjs","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\svp\\frontend\\medicine\\node_modules\\@mantine\\hooks\\esm\\use-local-storage\\use-local-storage.mjs":{"id":"(app-pages-browser)/./node_modules/@mantine/hooks/esm/use-local-storage/use-local-storage.mjs","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\svp\\frontend\\medicine\\node_modules\\@mantine\\hooks\\esm\\use-logger\\use-logger.mjs":{"id":"(app-pages-browser)/./node_modules/@mantine/hooks/esm/use-logger/use-logger.mjs","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\svp\\frontend\\medicine\\node_modules\\@mantine\\hooks\\esm\\use-map\\use-map.mjs":{"id":"(app-pages-browser)/./node_modules/@mantine/hooks/esm/use-map/use-map.mjs","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\svp\\frontend\\medicine\\node_modules\\@mantine\\hooks\\esm\\use-media-query\\use-media-query.mjs":{"id":"(app-pages-browser)/./node_modules/@mantine/hooks/esm/use-media-query/use-media-query.mjs","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\svp\\frontend\\medicine\\node_modules\\@mantine\\hooks\\esm\\use-merged-ref\\use-merged-ref.mjs":{"id":"(app-pages-browser)/./node_modules/@mantine/hooks/esm/use-merged-ref/use-merged-ref.mjs","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\svp\\frontend\\medicine\\node_modules\\@mantine\\hooks\\esm\\use-mounted\\use-mounted.mjs":{"id":"(app-pages-browser)/./node_modules/@mantine/hooks/esm/use-mounted/use-mounted.mjs","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\svp\\frontend\\medicine\\node_modules\\@mantine\\hooks\\esm\\use-mouse\\use-mouse.mjs":{"id":"(app-pages-browser)/./node_modules/@mantine/hooks/esm/use-mouse/use-mouse.mjs","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\svp\\frontend\\medicine\\node_modules\\@mantine\\hooks\\esm\\use-move\\use-move.mjs":{"id":"(app-pages-browser)/./node_modules/@mantine/hooks/esm/use-move/use-move.mjs","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\svp\\frontend\\medicine\\node_modules\\@mantine\\hooks\\esm\\use-mutation-observer\\use-mutation-observer.mjs":{"id":"(app-pages-browser)/./node_modules/@mantine/hooks/esm/use-mutation-observer/use-mutation-observer.mjs","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\svp\\frontend\\medicine\\node_modules\\@mantine\\hooks\\esm\\use-network\\use-network.mjs":{"id":"(app-pages-browser)/./node_modules/@mantine/hooks/esm/use-network/use-network.mjs","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\svp\\frontend\\medicine\\node_modules\\@mantine\\hooks\\esm\\use-orientation\\use-orientation.mjs":{"id":"(app-pages-browser)/./node_modules/@mantine/hooks/esm/use-orientation/use-orientation.mjs","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\svp\\frontend\\medicine\\node_modules\\@mantine\\hooks\\esm\\use-os\\use-os.mjs":{"id":"(app-pages-browser)/./node_modules/@mantine/hooks/esm/use-os/use-os.mjs","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\svp\\frontend\\medicine\\node_modules\\@mantine\\hooks\\esm\\use-page-leave\\use-page-leave.mjs":{"id":"(app-pages-browser)/./node_modules/@mantine/hooks/esm/use-page-leave/use-page-leave.mjs","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\svp\\frontend\\medicine\\node_modules\\@mantine\\hooks\\esm\\use-pagination\\use-pagination.mjs":{"id":"(app-pages-browser)/./node_modules/@mantine/hooks/esm/use-pagination/use-pagination.mjs","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\svp\\frontend\\medicine\\node_modules\\@mantine\\hooks\\esm\\use-previous\\use-previous.mjs":{"id":"(app-pages-browser)/./node_modules/@mantine/hooks/esm/use-previous/use-previous.mjs","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\svp\\frontend\\medicine\\node_modules\\@mantine\\hooks\\esm\\use-queue\\use-queue.mjs":{"id":"(app-pages-browser)/./node_modules/@mantine/hooks/esm/use-queue/use-queue.mjs","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\svp\\frontend\\medicine\\node_modules\\@mantine\\hooks\\esm\\use-radial-move\\use-radial-move.mjs":{"id":"(app-pages-browser)/./node_modules/@mantine/hooks/esm/use-radial-move/use-radial-move.mjs","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\svp\\frontend\\medicine\\node_modules\\@mantine\\hooks\\esm\\use-reduced-motion\\use-reduced-motion.mjs":{"id":"(app-pages-browser)/./node_modules/@mantine/hooks/esm/use-reduced-motion/use-reduced-motion.mjs","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\svp\\frontend\\medicine\\node_modules\\@mantine\\hooks\\esm\\use-resize-observer\\use-resize-observer.mjs":{"id":"(app-pages-browser)/./node_modules/@mantine/hooks/esm/use-resize-observer/use-resize-observer.mjs","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\svp\\frontend\\medicine\\node_modules\\@mantine\\hooks\\esm\\use-scroll-into-view\\use-scroll-into-view.mjs":{"id":"(app-pages-browser)/./node_modules/@mantine/hooks/esm/use-scroll-into-view/use-scroll-into-view.mjs","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\svp\\frontend\\medicine\\node_modules\\@mantine\\hooks\\esm\\use-scroll-spy\\use-scroll-spy.mjs":{"id":"(app-pages-browser)/./node_modules/@mantine/hooks/esm/use-scroll-spy/use-scroll-spy.mjs","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\svp\\frontend\\medicine\\node_modules\\@mantine\\hooks\\esm\\use-session-storage\\use-session-storage.mjs":{"id":"(app-pages-browser)/./node_modules/@mantine/hooks/esm/use-session-storage/use-session-storage.mjs","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\svp\\frontend\\medicine\\node_modules\\@mantine\\hooks\\esm\\use-set-state\\use-set-state.mjs":{"id":"(app-pages-browser)/./node_modules/@mantine/hooks/esm/use-set-state/use-set-state.mjs","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\svp\\frontend\\medicine\\node_modules\\@mantine\\hooks\\esm\\use-set\\use-set.mjs":{"id":"(app-pages-browser)/./node_modules/@mantine/hooks/esm/use-set/use-set.mjs","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\svp\\frontend\\medicine\\node_modules\\@mantine\\hooks\\esm\\use-shallow-effect\\use-shallow-effect.mjs":{"id":"(app-pages-browser)/./node_modules/@mantine/hooks/esm/use-shallow-effect/use-shallow-effect.mjs","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\svp\\frontend\\medicine\\node_modules\\@mantine\\hooks\\esm\\use-state-history\\use-state-history.mjs":{"id":"(app-pages-browser)/./node_modules/@mantine/hooks/esm/use-state-history/use-state-history.mjs","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\svp\\frontend\\medicine\\node_modules\\@mantine\\hooks\\esm\\use-text-selection\\use-text-selection.mjs":{"id":"(app-pages-browser)/./node_modules/@mantine/hooks/esm/use-text-selection/use-text-selection.mjs","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\svp\\frontend\\medicine\\node_modules\\@mantine\\hooks\\esm\\use-throttled-callback\\use-throttled-callback.mjs":{"id":"(app-pages-browser)/./node_modules/@mantine/hooks/esm/use-throttled-callback/use-throttled-callback.mjs","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\svp\\frontend\\medicine\\node_modules\\@mantine\\hooks\\esm\\use-throttled-state\\use-throttled-state.mjs":{"id":"(app-pages-browser)/./node_modules/@mantine/hooks/esm/use-throttled-state/use-throttled-state.mjs","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\svp\\frontend\\medicine\\node_modules\\@mantine\\hooks\\esm\\use-throttled-value\\use-throttled-value.mjs":{"id":"(app-pages-browser)/./node_modules/@mantine/hooks/esm/use-throttled-value/use-throttled-value.mjs","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\svp\\frontend\\medicine\\node_modules\\@mantine\\hooks\\esm\\use-timeout\\use-timeout.mjs":{"id":"(app-pages-browser)/./node_modules/@mantine/hooks/esm/use-timeout/use-timeout.mjs","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\svp\\frontend\\medicine\\node_modules\\@mantine\\hooks\\esm\\use-toggle\\use-toggle.mjs":{"id":"(app-pages-browser)/./node_modules/@mantine/hooks/esm/use-toggle/use-toggle.mjs","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\svp\\frontend\\medicine\\node_modules\\@mantine\\hooks\\esm\\use-uncontrolled\\use-uncontrolled.mjs":{"id":"(app-pages-browser)/./node_modules/@mantine/hooks/esm/use-uncontrolled/use-uncontrolled.mjs","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\svp\\frontend\\medicine\\node_modules\\@mantine\\hooks\\esm\\use-validated-state\\use-validated-state.mjs":{"id":"(app-pages-browser)/./node_modules/@mantine/hooks/esm/use-validated-state/use-validated-state.mjs","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\svp\\frontend\\medicine\\node_modules\\@mantine\\hooks\\esm\\use-viewport-size\\use-viewport-size.mjs":{"id":"(app-pages-browser)/./node_modules/@mantine/hooks/esm/use-viewport-size/use-viewport-size.mjs","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\svp\\frontend\\medicine\\node_modules\\@mantine\\hooks\\esm\\use-window-event\\use-window-event.mjs":{"id":"(app-pages-browser)/./node_modules/@mantine/hooks/esm/use-window-event/use-window-event.mjs","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\svp\\frontend\\medicine\\node_modules\\@mantine\\hooks\\esm\\use-window-scroll\\use-window-scroll.mjs":{"id":"(app-pages-browser)/./node_modules/@mantine/hooks/esm/use-window-scroll/use-window-scroll.mjs","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\svp\\frontend\\medicine\\node_modules\\@mantine\\hooks\\esm\\utils\\clamp\\clamp.mjs":{"id":"(app-pages-browser)/./node_modules/@mantine/hooks/esm/utils/clamp/clamp.mjs","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\svp\\frontend\\medicine\\node_modules\\@mantine\\hooks\\esm\\utils\\lower-first\\lower-first.mjs":{"id":"(app-pages-browser)/./node_modules/@mantine/hooks/esm/utils/lower-first/lower-first.mjs","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\svp\\frontend\\medicine\\node_modules\\@mantine\\hooks\\esm\\utils\\random-id\\random-id.mjs":{"id":"(app-pages-browser)/./node_modules/@mantine/hooks/esm/utils/random-id/random-id.mjs","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\svp\\frontend\\medicine\\node_modules\\@mantine\\hooks\\esm\\utils\\range\\range.mjs":{"id":"(app-pages-browser)/./node_modules/@mantine/hooks/esm/utils/range/range.mjs","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\svp\\frontend\\medicine\\node_modules\\@mantine\\hooks\\esm\\utils\\shallow-equal\\shallow-equal.mjs":{"id":"(app-pages-browser)/./node_modules/@mantine/hooks/esm/utils/shallow-equal/shallow-equal.mjs","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\svp\\frontend\\medicine\\node_modules\\@mantine\\hooks\\esm\\utils\\upper-first\\upper-first.mjs":{"id":"(app-pages-browser)/./node_modules/@mantine/hooks/esm/utils/upper-first/upper-first.mjs","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\svp\\frontend\\medicine\\node_modules\\@mantine\\modals\\esm\\events.mjs":{"id":"(app-pages-browser)/./node_modules/@mantine/modals/esm/events.mjs","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\svp\\frontend\\medicine\\node_modules\\@mantine\\modals\\esm\\ModalsProvider.mjs":{"id":"(app-pages-browser)/./node_modules/@mantine/modals/esm/ModalsProvider.mjs","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\svp\\frontend\\medicine\\node_modules\\@mantine\\modals\\esm\\use-modals\\use-modals.mjs":{"id":"(app-pages-browser)/./node_modules/@mantine/modals/esm/use-modals/use-modals.mjs","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\svp\\frontend\\medicine\\node_modules\\@mantine\\notifications\\esm\\Notifications.mjs":{"id":"(app-pages-browser)/./node_modules/@mantine/notifications/esm/Notifications.mjs","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\svp\\frontend\\medicine\\node_modules\\@mantine\\notifications\\esm\\notifications.store.mjs":{"id":"(app-pages-browser)/./node_modules/@mantine/notifications/esm/notifications.store.mjs","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\svp\\frontend\\medicine\\node_modules\\next\\font\\google\\target.css?{\"path\":\"src\\\\app\\\\layout.tsx\",\"import\":\"Geist\",\"arguments\":[{\"variable\":\"--font-geist-sans\",\"subsets\":[\"latin\"]}],\"variableName\":\"geistSans\"}":{"id":"(app-pages-browser)/./node_modules/next/font/google/target.css?{\"path\":\"src\\\\app\\\\layout.tsx\",\"import\":\"Geist\",\"arguments\":[{\"variable\":\"--font-geist-sans\",\"subsets\":[\"latin\"]}],\"variableName\":\"geistSans\"}","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\svp\\frontend\\medicine\\node_modules\\next\\font\\google\\target.css?{\"path\":\"src\\\\app\\\\layout.tsx\",\"import\":\"Geist_Mono\",\"arguments\":[{\"variable\":\"--font-geist-mono\",\"subsets\":[\"latin\"]}],\"variableName\":\"geistMono\"}":{"id":"(app-pages-browser)/./node_modules/next/font/google/target.css?{\"path\":\"src\\\\app\\\\layout.tsx\",\"import\":\"Geist_Mono\",\"arguments\":[{\"variable\":\"--font-geist-mono\",\"subsets\":[\"latin\"]}],\"variableName\":\"geistMono\"}","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\svp\\frontend\\medicine\\node_modules\\@mantine\\notifications\\styles.css":{"id":"(app-pages-browser)/./node_modules/@mantine/notifications/styles.css","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\svp\\frontend\\medicine\\node_modules\\nextjs-toploader\\dist\\index.js":{"id":"(app-pages-browser)/./node_modules/nextjs-toploader/dist/index.js","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\svp\\frontend\\medicine\\node_modules\\simplebar-react\\dist\\simplebar.min.css":{"id":"(app-pages-browser)/./node_modules/simplebar-react/dist/simplebar.min.css","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\svp\\frontend\\medicine\\src\\components\\scrollToTop\\ScrollToTop.tsx":{"id":"(app-pages-browser)/./src/components/scrollToTop/ScrollToTop.tsx","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\svp\\frontend\\medicine\\src\\hooks\\LocaleProvider.tsx":{"id":"(app-pages-browser)/./src/hooks/LocaleProvider.tsx","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\svp\\frontend\\medicine\\src\\hooks\\useAuth.tsx":{"id":"(app-pages-browser)/./src/hooks/useAuth.tsx","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\svp\\frontend\\medicine\\src\\style\\globals.css":{"id":"(app-pages-browser)/./src/style/globals.css","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\svp\\frontend\\medicine\\src\\app\\(dashboard)\\CalendarLayoutClient.tsx":{"id":"(app-pages-browser)/./src/app/(dashboard)/CalendarLayoutClient.tsx","name":"*","chunks":["app/(dashboard)/layout","static/chunks/app/(dashboard)/layout.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\svp\\frontend\\medicine\\src\\app\\(dashboard)\\web\\page.tsx":{"id":"(app-pages-browser)/./src/app/(dashboard)/web/page.tsx","name":"*","chunks":["app/(dashboard)/web/page","static/chunks/app/(dashboard)/web/page.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\svp\\frontend\\medicine\\node_modules\\next\\dist\\client\\components\\client-page.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/client-page.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\svp\\frontend\\medicine\\node_modules\\next\\dist\\esm\\client\\components\\client-page.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/client-page.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\svp\\frontend\\medicine\\node_modules\\next\\dist\\client\\components\\client-segment.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/client-segment.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\svp\\frontend\\medicine\\node_modules\\next\\dist\\esm\\client\\components\\client-segment.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/client-segment.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\svp\\frontend\\medicine\\node_modules\\next\\dist\\client\\components\\error-boundary.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/error-boundary.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\svp\\frontend\\medicine\\node_modules\\next\\dist\\esm\\client\\components\\error-boundary.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/error-boundary.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\svp\\frontend\\medicine\\node_modules\\next\\dist\\client\\components\\http-access-fallback\\error-boundary.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\svp\\frontend\\medicine\\node_modules\\next\\dist\\esm\\client\\components\\http-access-fallback\\error-boundary.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\svp\\frontend\\medicine\\node_modules\\next\\dist\\client\\components\\layout-router.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/layout-router.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\svp\\frontend\\medicine\\node_modules\\next\\dist\\esm\\client\\components\\layout-router.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/layout-router.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\svp\\frontend\\medicine\\node_modules\\next\\dist\\client\\components\\metadata\\async-metadata.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/metadata/async-metadata.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\svp\\frontend\\medicine\\node_modules\\next\\dist\\esm\\client\\components\\metadata\\async-metadata.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/metadata/async-metadata.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\svp\\frontend\\medicine\\node_modules\\next\\dist\\client\\components\\metadata\\metadata-boundary.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\svp\\frontend\\medicine\\node_modules\\next\\dist\\esm\\client\\components\\metadata\\metadata-boundary.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\svp\\frontend\\medicine\\node_modules\\next\\dist\\client\\components\\render-from-template-context.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/render-from-template-context.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\svp\\frontend\\medicine\\node_modules\\next\\dist\\esm\\client\\components\\render-from-template-context.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/render-from-template-context.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\svp\\frontend\\medicine\\src\\contexts\\DayslotGetter.tsx":{"id":"(app-pages-browser)/./src/contexts/DayslotGetter.tsx","name":"*","chunks":[],"async":false},"C:\\Users\\<USER>\\Desktop\\svp\\frontend\\medicine\\src\\app\\(dashboard)\\appointments\\page.tsx":{"id":"(app-pages-browser)/./src/app/(dashboard)/appointments/page.tsx","name":"*","chunks":[],"async":false}},"entryCSSFiles":{"C:\\Users\\<USER>\\Desktop\\svp\\frontend\\medicine\\src\\":[],"C:\\Users\\<USER>\\Desktop\\svp\\frontend\\medicine\\src\\app\\layout":[{"inlined":false,"path":"static/css/app/layout.css"}],"C:\\Users\\<USER>\\Desktop\\svp\\frontend\\medicine\\src\\app\\(dashboard)\\layout":[{"inlined":false,"path":"static/css/app/(dashboard)/layout.css"}],"C:\\Users\\<USER>\\Desktop\\svp\\frontend\\medicine\\src\\app\\(dashboard)\\web\\page":[{"inlined":false,"path":"static/css/app/(dashboard)/web/page.css"}]},"rscModuleMapping":{"(app-pages-browser)/./node_modules/@mantine/core/esm/components/Accordion/Accordion.mjs":{"*":{"id":"(rsc)/./node_modules/@mantine/core/esm/components/Accordion/Accordion.mjs","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@mantine/core/esm/components/Accordion/AccordionChevron.mjs":{"*":{"id":"(rsc)/./node_modules/@mantine/core/esm/components/Accordion/AccordionChevron.mjs","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@mantine/core/esm/components/Accordion/AccordionControl/AccordionControl.mjs":{"*":{"id":"(rsc)/./node_modules/@mantine/core/esm/components/Accordion/AccordionControl/AccordionControl.mjs","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@mantine/core/esm/components/Accordion/AccordionItem/AccordionItem.mjs":{"*":{"id":"(rsc)/./node_modules/@mantine/core/esm/components/Accordion/AccordionItem/AccordionItem.mjs","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@mantine/core/esm/components/Accordion/AccordionPanel/AccordionPanel.mjs":{"*":{"id":"(rsc)/./node_modules/@mantine/core/esm/components/Accordion/AccordionPanel/AccordionPanel.mjs","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@mantine/core/esm/components/ActionIcon/ActionIcon.mjs":{"*":{"id":"(rsc)/./node_modules/@mantine/core/esm/components/ActionIcon/ActionIcon.mjs","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@mantine/core/esm/components/ActionIcon/ActionIconGroup/ActionIconGroup.mjs":{"*":{"id":"(rsc)/./node_modules/@mantine/core/esm/components/ActionIcon/ActionIconGroup/ActionIconGroup.mjs","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@mantine/core/esm/components/ActionIcon/ActionIconGroupSection/ActionIconGroupSection.mjs":{"*":{"id":"(rsc)/./node_modules/@mantine/core/esm/components/ActionIcon/ActionIconGroupSection/ActionIconGroupSection.mjs","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@mantine/core/esm/components/Affix/Affix.mjs":{"*":{"id":"(rsc)/./node_modules/@mantine/core/esm/components/Affix/Affix.mjs","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@mantine/core/esm/components/Alert/Alert.mjs":{"*":{"id":"(rsc)/./node_modules/@mantine/core/esm/components/Alert/Alert.mjs","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@mantine/core/esm/components/Anchor/Anchor.mjs":{"*":{"id":"(rsc)/./node_modules/@mantine/core/esm/components/Anchor/Anchor.mjs","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@mantine/core/esm/components/AngleSlider/AngleSlider.mjs":{"*":{"id":"(rsc)/./node_modules/@mantine/core/esm/components/AngleSlider/AngleSlider.mjs","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@mantine/core/esm/components/AppShell/AppShell.mjs":{"*":{"id":"(rsc)/./node_modules/@mantine/core/esm/components/AppShell/AppShell.mjs","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@mantine/core/esm/components/AppShell/AppShellAside/AppShellAside.mjs":{"*":{"id":"(rsc)/./node_modules/@mantine/core/esm/components/AppShell/AppShellAside/AppShellAside.mjs","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@mantine/core/esm/components/AppShell/AppShellFooter/AppShellFooter.mjs":{"*":{"id":"(rsc)/./node_modules/@mantine/core/esm/components/AppShell/AppShellFooter/AppShellFooter.mjs","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@mantine/core/esm/components/AppShell/AppShellHeader/AppShellHeader.mjs":{"*":{"id":"(rsc)/./node_modules/@mantine/core/esm/components/AppShell/AppShellHeader/AppShellHeader.mjs","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@mantine/core/esm/components/AppShell/AppShellMain/AppShellMain.mjs":{"*":{"id":"(rsc)/./node_modules/@mantine/core/esm/components/AppShell/AppShellMain/AppShellMain.mjs","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@mantine/core/esm/components/AppShell/AppShellNavbar/AppShellNavbar.mjs":{"*":{"id":"(rsc)/./node_modules/@mantine/core/esm/components/AppShell/AppShellNavbar/AppShellNavbar.mjs","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@mantine/core/esm/components/AppShell/AppShellSection/AppShellSection.mjs":{"*":{"id":"(rsc)/./node_modules/@mantine/core/esm/components/AppShell/AppShellSection/AppShellSection.mjs","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@mantine/core/esm/components/AspectRatio/AspectRatio.mjs":{"*":{"id":"(rsc)/./node_modules/@mantine/core/esm/components/AspectRatio/AspectRatio.mjs","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@mantine/core/esm/components/Autocomplete/Autocomplete.mjs":{"*":{"id":"(rsc)/./node_modules/@mantine/core/esm/components/Autocomplete/Autocomplete.mjs","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@mantine/core/esm/components/Avatar/Avatar.mjs":{"*":{"id":"(rsc)/./node_modules/@mantine/core/esm/components/Avatar/Avatar.mjs","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@mantine/core/esm/components/Avatar/AvatarGroup/AvatarGroup.mjs":{"*":{"id":"(rsc)/./node_modules/@mantine/core/esm/components/Avatar/AvatarGroup/AvatarGroup.mjs","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@mantine/core/esm/components/BackgroundImage/BackgroundImage.mjs":{"*":{"id":"(rsc)/./node_modules/@mantine/core/esm/components/BackgroundImage/BackgroundImage.mjs","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@mantine/core/esm/components/Badge/Badge.mjs":{"*":{"id":"(rsc)/./node_modules/@mantine/core/esm/components/Badge/Badge.mjs","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@mantine/core/esm/components/Blockquote/Blockquote.mjs":{"*":{"id":"(rsc)/./node_modules/@mantine/core/esm/components/Blockquote/Blockquote.mjs","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@mantine/core/esm/components/Breadcrumbs/Breadcrumbs.mjs":{"*":{"id":"(rsc)/./node_modules/@mantine/core/esm/components/Breadcrumbs/Breadcrumbs.mjs","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@mantine/core/esm/components/Burger/Burger.mjs":{"*":{"id":"(rsc)/./node_modules/@mantine/core/esm/components/Burger/Burger.mjs","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@mantine/core/esm/components/Button/Button.mjs":{"*":{"id":"(rsc)/./node_modules/@mantine/core/esm/components/Button/Button.mjs","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@mantine/core/esm/components/Button/ButtonGroup/ButtonGroup.mjs":{"*":{"id":"(rsc)/./node_modules/@mantine/core/esm/components/Button/ButtonGroup/ButtonGroup.mjs","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@mantine/core/esm/components/Button/ButtonGroupSection/ButtonGroupSection.mjs":{"*":{"id":"(rsc)/./node_modules/@mantine/core/esm/components/Button/ButtonGroupSection/ButtonGroupSection.mjs","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@mantine/core/esm/components/Card/Card.mjs":{"*":{"id":"(rsc)/./node_modules/@mantine/core/esm/components/Card/Card.mjs","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@mantine/core/esm/components/Card/CardSection/CardSection.mjs":{"*":{"id":"(rsc)/./node_modules/@mantine/core/esm/components/Card/CardSection/CardSection.mjs","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@mantine/core/esm/components/Center/Center.mjs":{"*":{"id":"(rsc)/./node_modules/@mantine/core/esm/components/Center/Center.mjs","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@mantine/core/esm/components/Checkbox/Checkbox.mjs":{"*":{"id":"(rsc)/./node_modules/@mantine/core/esm/components/Checkbox/Checkbox.mjs","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@mantine/core/esm/components/Checkbox/CheckboxCard/CheckboxCard.context.mjs":{"*":{"id":"(rsc)/./node_modules/@mantine/core/esm/components/Checkbox/CheckboxCard/CheckboxCard.context.mjs","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@mantine/core/esm/components/Checkbox/CheckboxCard/CheckboxCard.mjs":{"*":{"id":"(rsc)/./node_modules/@mantine/core/esm/components/Checkbox/CheckboxCard/CheckboxCard.mjs","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@mantine/core/esm/components/Checkbox/CheckboxGroup.context.mjs":{"*":{"id":"(rsc)/./node_modules/@mantine/core/esm/components/Checkbox/CheckboxGroup.context.mjs","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@mantine/core/esm/components/Checkbox/CheckboxGroup/CheckboxGroup.mjs":{"*":{"id":"(rsc)/./node_modules/@mantine/core/esm/components/Checkbox/CheckboxGroup/CheckboxGroup.mjs","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@mantine/core/esm/components/Checkbox/CheckboxIndicator/CheckboxIndicator.mjs":{"*":{"id":"(rsc)/./node_modules/@mantine/core/esm/components/Checkbox/CheckboxIndicator/CheckboxIndicator.mjs","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@mantine/core/esm/components/Checkbox/CheckIcon.mjs":{"*":{"id":"(rsc)/./node_modules/@mantine/core/esm/components/Checkbox/CheckIcon.mjs","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@mantine/core/esm/components/Chip/Chip.mjs":{"*":{"id":"(rsc)/./node_modules/@mantine/core/esm/components/Chip/Chip.mjs","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@mantine/core/esm/components/Chip/ChipGroup/ChipGroup.mjs":{"*":{"id":"(rsc)/./node_modules/@mantine/core/esm/components/Chip/ChipGroup/ChipGroup.mjs","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@mantine/core/esm/components/CloseButton/CloseButton.mjs":{"*":{"id":"(rsc)/./node_modules/@mantine/core/esm/components/CloseButton/CloseButton.mjs","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@mantine/core/esm/components/CloseButton/CloseIcon.mjs":{"*":{"id":"(rsc)/./node_modules/@mantine/core/esm/components/CloseButton/CloseIcon.mjs","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@mantine/core/esm/components/Code/Code.mjs":{"*":{"id":"(rsc)/./node_modules/@mantine/core/esm/components/Code/Code.mjs","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@mantine/core/esm/components/Collapse/Collapse.mjs":{"*":{"id":"(rsc)/./node_modules/@mantine/core/esm/components/Collapse/Collapse.mjs","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@mantine/core/esm/components/ColorInput/ColorInput.mjs":{"*":{"id":"(rsc)/./node_modules/@mantine/core/esm/components/ColorInput/ColorInput.mjs","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@mantine/core/esm/components/ColorPicker/AlphaSlider/AlphaSlider.mjs":{"*":{"id":"(rsc)/./node_modules/@mantine/core/esm/components/ColorPicker/AlphaSlider/AlphaSlider.mjs","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@mantine/core/esm/components/ColorPicker/ColorPicker.mjs":{"*":{"id":"(rsc)/./node_modules/@mantine/core/esm/components/ColorPicker/ColorPicker.mjs","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@mantine/core/esm/components/ColorPicker/converters/converters.mjs":{"*":{"id":"(rsc)/./node_modules/@mantine/core/esm/components/ColorPicker/converters/converters.mjs","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@mantine/core/esm/components/ColorPicker/converters/parsers.mjs":{"*":{"id":"(rsc)/./node_modules/@mantine/core/esm/components/ColorPicker/converters/parsers.mjs","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@mantine/core/esm/components/ColorPicker/HueSlider/HueSlider.mjs":{"*":{"id":"(rsc)/./node_modules/@mantine/core/esm/components/ColorPicker/HueSlider/HueSlider.mjs","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@mantine/core/esm/components/ColorSwatch/ColorSwatch.mjs":{"*":{"id":"(rsc)/./node_modules/@mantine/core/esm/components/ColorSwatch/ColorSwatch.mjs","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@mantine/core/esm/components/Combobox/Combobox.mjs":{"*":{"id":"(rsc)/./node_modules/@mantine/core/esm/components/Combobox/Combobox.mjs","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@mantine/core/esm/components/Combobox/ComboboxChevron/ComboboxChevron.mjs":{"*":{"id":"(rsc)/./node_modules/@mantine/core/esm/components/Combobox/ComboboxChevron/ComboboxChevron.mjs","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@mantine/core/esm/components/Combobox/ComboboxClearButton/ComboboxClearButton.mjs":{"*":{"id":"(rsc)/./node_modules/@mantine/core/esm/components/Combobox/ComboboxClearButton/ComboboxClearButton.mjs","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@mantine/core/esm/components/Combobox/ComboboxDropdown/ComboboxDropdown.mjs":{"*":{"id":"(rsc)/./node_modules/@mantine/core/esm/components/Combobox/ComboboxDropdown/ComboboxDropdown.mjs","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@mantine/core/esm/components/Combobox/ComboboxDropdownTarget/ComboboxDropdownTarget.mjs":{"*":{"id":"(rsc)/./node_modules/@mantine/core/esm/components/Combobox/ComboboxDropdownTarget/ComboboxDropdownTarget.mjs","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@mantine/core/esm/components/Combobox/ComboboxEmpty/ComboboxEmpty.mjs":{"*":{"id":"(rsc)/./node_modules/@mantine/core/esm/components/Combobox/ComboboxEmpty/ComboboxEmpty.mjs","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@mantine/core/esm/components/Combobox/ComboboxEventsTarget/ComboboxEventsTarget.mjs":{"*":{"id":"(rsc)/./node_modules/@mantine/core/esm/components/Combobox/ComboboxEventsTarget/ComboboxEventsTarget.mjs","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@mantine/core/esm/components/Combobox/ComboboxFooter/ComboboxFooter.mjs":{"*":{"id":"(rsc)/./node_modules/@mantine/core/esm/components/Combobox/ComboboxFooter/ComboboxFooter.mjs","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@mantine/core/esm/components/Combobox/ComboboxGroup/ComboboxGroup.mjs":{"*":{"id":"(rsc)/./node_modules/@mantine/core/esm/components/Combobox/ComboboxGroup/ComboboxGroup.mjs","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@mantine/core/esm/components/Combobox/ComboboxHeader/ComboboxHeader.mjs":{"*":{"id":"(rsc)/./node_modules/@mantine/core/esm/components/Combobox/ComboboxHeader/ComboboxHeader.mjs","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@mantine/core/esm/components/Combobox/ComboboxHiddenInput/ComboboxHiddenInput.mjs":{"*":{"id":"(rsc)/./node_modules/@mantine/core/esm/components/Combobox/ComboboxHiddenInput/ComboboxHiddenInput.mjs","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@mantine/core/esm/components/Combobox/ComboboxOption/ComboboxOption.mjs":{"*":{"id":"(rsc)/./node_modules/@mantine/core/esm/components/Combobox/ComboboxOption/ComboboxOption.mjs","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@mantine/core/esm/components/Combobox/ComboboxOptions/ComboboxOptions.mjs":{"*":{"id":"(rsc)/./node_modules/@mantine/core/esm/components/Combobox/ComboboxOptions/ComboboxOptions.mjs","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@mantine/core/esm/components/Combobox/ComboboxSearch/ComboboxSearch.mjs":{"*":{"id":"(rsc)/./node_modules/@mantine/core/esm/components/Combobox/ComboboxSearch/ComboboxSearch.mjs","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@mantine/core/esm/components/Combobox/ComboboxTarget/ComboboxTarget.mjs":{"*":{"id":"(rsc)/./node_modules/@mantine/core/esm/components/Combobox/ComboboxTarget/ComboboxTarget.mjs","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@mantine/core/esm/components/Combobox/get-options-lockup/get-options-lockup.mjs":{"*":{"id":"(rsc)/./node_modules/@mantine/core/esm/components/Combobox/get-options-lockup/get-options-lockup.mjs","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@mantine/core/esm/components/Combobox/get-parsed-combobox-data/get-parsed-combobox-data.mjs":{"*":{"id":"(rsc)/./node_modules/@mantine/core/esm/components/Combobox/get-parsed-combobox-data/get-parsed-combobox-data.mjs","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@mantine/core/esm/components/Combobox/OptionsDropdown/default-options-filter.mjs":{"*":{"id":"(rsc)/./node_modules/@mantine/core/esm/components/Combobox/OptionsDropdown/default-options-filter.mjs","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@mantine/core/esm/components/Combobox/OptionsDropdown/is-options-group.mjs":{"*":{"id":"(rsc)/./node_modules/@mantine/core/esm/components/Combobox/OptionsDropdown/is-options-group.mjs","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@mantine/core/esm/components/Combobox/OptionsDropdown/OptionsDropdown.mjs":{"*":{"id":"(rsc)/./node_modules/@mantine/core/esm/components/Combobox/OptionsDropdown/OptionsDropdown.mjs","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@mantine/core/esm/components/Combobox/use-combobox-target-props/use-combobox-target-props.mjs":{"*":{"id":"(rsc)/./node_modules/@mantine/core/esm/components/Combobox/use-combobox-target-props/use-combobox-target-props.mjs","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@mantine/core/esm/components/Combobox/use-combobox/use-combobox.mjs":{"*":{"id":"(rsc)/./node_modules/@mantine/core/esm/components/Combobox/use-combobox/use-combobox.mjs","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@mantine/core/esm/components/Combobox/use-combobox/use-virtualized-combobox.mjs":{"*":{"id":"(rsc)/./node_modules/@mantine/core/esm/components/Combobox/use-combobox/use-virtualized-combobox.mjs","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@mantine/core/esm/components/Container/Container.mjs":{"*":{"id":"(rsc)/./node_modules/@mantine/core/esm/components/Container/Container.mjs","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@mantine/core/esm/components/CopyButton/CopyButton.mjs":{"*":{"id":"(rsc)/./node_modules/@mantine/core/esm/components/CopyButton/CopyButton.mjs","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@mantine/core/esm/components/Dialog/Dialog.mjs":{"*":{"id":"(rsc)/./node_modules/@mantine/core/esm/components/Dialog/Dialog.mjs","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@mantine/core/esm/components/Divider/Divider.mjs":{"*":{"id":"(rsc)/./node_modules/@mantine/core/esm/components/Divider/Divider.mjs","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@mantine/core/esm/components/Drawer/Drawer.mjs":{"*":{"id":"(rsc)/./node_modules/@mantine/core/esm/components/Drawer/Drawer.mjs","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@mantine/core/esm/components/Drawer/DrawerBody.mjs":{"*":{"id":"(rsc)/./node_modules/@mantine/core/esm/components/Drawer/DrawerBody.mjs","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@mantine/core/esm/components/Drawer/DrawerCloseButton.mjs":{"*":{"id":"(rsc)/./node_modules/@mantine/core/esm/components/Drawer/DrawerCloseButton.mjs","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@mantine/core/esm/components/Drawer/DrawerContent.mjs":{"*":{"id":"(rsc)/./node_modules/@mantine/core/esm/components/Drawer/DrawerContent.mjs","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@mantine/core/esm/components/Drawer/DrawerHeader.mjs":{"*":{"id":"(rsc)/./node_modules/@mantine/core/esm/components/Drawer/DrawerHeader.mjs","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@mantine/core/esm/components/Drawer/DrawerOverlay.mjs":{"*":{"id":"(rsc)/./node_modules/@mantine/core/esm/components/Drawer/DrawerOverlay.mjs","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@mantine/core/esm/components/Drawer/DrawerRoot.mjs":{"*":{"id":"(rsc)/./node_modules/@mantine/core/esm/components/Drawer/DrawerRoot.mjs","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@mantine/core/esm/components/Drawer/DrawerStack.mjs":{"*":{"id":"(rsc)/./node_modules/@mantine/core/esm/components/Drawer/DrawerStack.mjs","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@mantine/core/esm/components/Drawer/DrawerTitle.mjs":{"*":{"id":"(rsc)/./node_modules/@mantine/core/esm/components/Drawer/DrawerTitle.mjs","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@mantine/core/esm/components/Fieldset/Fieldset.mjs":{"*":{"id":"(rsc)/./node_modules/@mantine/core/esm/components/Fieldset/Fieldset.mjs","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@mantine/core/esm/components/FileButton/FileButton.mjs":{"*":{"id":"(rsc)/./node_modules/@mantine/core/esm/components/FileButton/FileButton.mjs","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@mantine/core/esm/components/FileInput/FileInput.mjs":{"*":{"id":"(rsc)/./node_modules/@mantine/core/esm/components/FileInput/FileInput.mjs","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@mantine/core/esm/components/Flex/flex-props.mjs":{"*":{"id":"(rsc)/./node_modules/@mantine/core/esm/components/Flex/flex-props.mjs","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@mantine/core/esm/components/Flex/Flex.mjs":{"*":{"id":"(rsc)/./node_modules/@mantine/core/esm/components/Flex/Flex.mjs","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@mantine/core/esm/components/Floating/FloatingArrow/FloatingArrow.mjs":{"*":{"id":"(rsc)/./node_modules/@mantine/core/esm/components/Floating/FloatingArrow/FloatingArrow.mjs","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@mantine/core/esm/components/Floating/get-floating-position/get-floating-position.mjs":{"*":{"id":"(rsc)/./node_modules/@mantine/core/esm/components/Floating/get-floating-position/get-floating-position.mjs","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@mantine/core/esm/components/Floating/use-delayed-hover.mjs":{"*":{"id":"(rsc)/./node_modules/@mantine/core/esm/components/Floating/use-delayed-hover.mjs","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@mantine/core/esm/components/Floating/use-floating-auto-update.mjs":{"*":{"id":"(rsc)/./node_modules/@mantine/core/esm/components/Floating/use-floating-auto-update.mjs","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@mantine/core/esm/components/FloatingIndicator/FloatingIndicator.mjs":{"*":{"id":"(rsc)/./node_modules/@mantine/core/esm/components/FloatingIndicator/FloatingIndicator.mjs","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@mantine/core/esm/components/FocusTrap/FocusTrap.mjs":{"*":{"id":"(rsc)/./node_modules/@mantine/core/esm/components/FocusTrap/FocusTrap.mjs","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@mantine/core/esm/components/Grid/Grid.mjs":{"*":{"id":"(rsc)/./node_modules/@mantine/core/esm/components/Grid/Grid.mjs","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@mantine/core/esm/components/Grid/GridCol/GridCol.mjs":{"*":{"id":"(rsc)/./node_modules/@mantine/core/esm/components/Grid/GridCol/GridCol.mjs","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@mantine/core/esm/components/Group/Group.mjs":{"*":{"id":"(rsc)/./node_modules/@mantine/core/esm/components/Group/Group.mjs","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@mantine/core/esm/components/Highlight/Highlight.mjs":{"*":{"id":"(rsc)/./node_modules/@mantine/core/esm/components/Highlight/Highlight.mjs","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@mantine/core/esm/components/HoverCard/HoverCard.mjs":{"*":{"id":"(rsc)/./node_modules/@mantine/core/esm/components/HoverCard/HoverCard.mjs","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@mantine/core/esm/components/HoverCard/HoverCardDropdown/HoverCardDropdown.mjs":{"*":{"id":"(rsc)/./node_modules/@mantine/core/esm/components/HoverCard/HoverCardDropdown/HoverCardDropdown.mjs","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@mantine/core/esm/components/HoverCard/HoverCardTarget/HoverCardTarget.mjs":{"*":{"id":"(rsc)/./node_modules/@mantine/core/esm/components/HoverCard/HoverCardTarget/HoverCardTarget.mjs","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@mantine/core/esm/components/Image/Image.mjs":{"*":{"id":"(rsc)/./node_modules/@mantine/core/esm/components/Image/Image.mjs","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@mantine/core/esm/components/Indicator/Indicator.mjs":{"*":{"id":"(rsc)/./node_modules/@mantine/core/esm/components/Indicator/Indicator.mjs","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@mantine/core/esm/components/Input/Input.mjs":{"*":{"id":"(rsc)/./node_modules/@mantine/core/esm/components/Input/Input.mjs","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@mantine/core/esm/components/Input/InputClearButton/InputClearButton.mjs":{"*":{"id":"(rsc)/./node_modules/@mantine/core/esm/components/Input/InputClearButton/InputClearButton.mjs","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@mantine/core/esm/components/Input/InputDescription/InputDescription.mjs":{"*":{"id":"(rsc)/./node_modules/@mantine/core/esm/components/Input/InputDescription/InputDescription.mjs","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@mantine/core/esm/components/Input/InputError/InputError.mjs":{"*":{"id":"(rsc)/./node_modules/@mantine/core/esm/components/Input/InputError/InputError.mjs","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@mantine/core/esm/components/Input/InputLabel/InputLabel.mjs":{"*":{"id":"(rsc)/./node_modules/@mantine/core/esm/components/Input/InputLabel/InputLabel.mjs","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@mantine/core/esm/components/Input/InputPlaceholder/InputPlaceholder.mjs":{"*":{"id":"(rsc)/./node_modules/@mantine/core/esm/components/Input/InputPlaceholder/InputPlaceholder.mjs","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@mantine/core/esm/components/Input/InputWrapper.context.mjs":{"*":{"id":"(rsc)/./node_modules/@mantine/core/esm/components/Input/InputWrapper.context.mjs","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@mantine/core/esm/components/Input/InputWrapper/InputWrapper.mjs":{"*":{"id":"(rsc)/./node_modules/@mantine/core/esm/components/Input/InputWrapper/InputWrapper.mjs","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@mantine/core/esm/components/Input/use-input-props.mjs":{"*":{"id":"(rsc)/./node_modules/@mantine/core/esm/components/Input/use-input-props.mjs","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@mantine/core/esm/components/InputBase/InputBase.mjs":{"*":{"id":"(rsc)/./node_modules/@mantine/core/esm/components/InputBase/InputBase.mjs","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@mantine/core/esm/components/JsonInput/JsonInput.mjs":{"*":{"id":"(rsc)/./node_modules/@mantine/core/esm/components/JsonInput/JsonInput.mjs","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@mantine/core/esm/components/Kbd/Kbd.mjs":{"*":{"id":"(rsc)/./node_modules/@mantine/core/esm/components/Kbd/Kbd.mjs","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@mantine/core/esm/components/List/List.mjs":{"*":{"id":"(rsc)/./node_modules/@mantine/core/esm/components/List/List.mjs","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@mantine/core/esm/components/List/ListItem/ListItem.mjs":{"*":{"id":"(rsc)/./node_modules/@mantine/core/esm/components/List/ListItem/ListItem.mjs","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@mantine/core/esm/components/Loader/Loader.mjs":{"*":{"id":"(rsc)/./node_modules/@mantine/core/esm/components/Loader/Loader.mjs","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@mantine/core/esm/components/LoadingOverlay/LoadingOverlay.mjs":{"*":{"id":"(rsc)/./node_modules/@mantine/core/esm/components/LoadingOverlay/LoadingOverlay.mjs","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@mantine/core/esm/components/Mark/Mark.mjs":{"*":{"id":"(rsc)/./node_modules/@mantine/core/esm/components/Mark/Mark.mjs","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@mantine/core/esm/components/Menu/Menu.mjs":{"*":{"id":"(rsc)/./node_modules/@mantine/core/esm/components/Menu/Menu.mjs","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@mantine/core/esm/components/Menu/MenuDivider/MenuDivider.mjs":{"*":{"id":"(rsc)/./node_modules/@mantine/core/esm/components/Menu/MenuDivider/MenuDivider.mjs","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@mantine/core/esm/components/Menu/MenuDropdown/MenuDropdown.mjs":{"*":{"id":"(rsc)/./node_modules/@mantine/core/esm/components/Menu/MenuDropdown/MenuDropdown.mjs","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@mantine/core/esm/components/Menu/MenuItem/MenuItem.mjs":{"*":{"id":"(rsc)/./node_modules/@mantine/core/esm/components/Menu/MenuItem/MenuItem.mjs","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@mantine/core/esm/components/Menu/MenuLabel/MenuLabel.mjs":{"*":{"id":"(rsc)/./node_modules/@mantine/core/esm/components/Menu/MenuLabel/MenuLabel.mjs","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@mantine/core/esm/components/Menu/MenuTarget/MenuTarget.mjs":{"*":{"id":"(rsc)/./node_modules/@mantine/core/esm/components/Menu/MenuTarget/MenuTarget.mjs","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@mantine/core/esm/components/Modal/Modal.mjs":{"*":{"id":"(rsc)/./node_modules/@mantine/core/esm/components/Modal/Modal.mjs","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@mantine/core/esm/components/Modal/ModalBody.mjs":{"*":{"id":"(rsc)/./node_modules/@mantine/core/esm/components/Modal/ModalBody.mjs","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@mantine/core/esm/components/Modal/ModalCloseButton.mjs":{"*":{"id":"(rsc)/./node_modules/@mantine/core/esm/components/Modal/ModalCloseButton.mjs","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@mantine/core/esm/components/Modal/ModalContent.mjs":{"*":{"id":"(rsc)/./node_modules/@mantine/core/esm/components/Modal/ModalContent.mjs","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@mantine/core/esm/components/Modal/ModalHeader.mjs":{"*":{"id":"(rsc)/./node_modules/@mantine/core/esm/components/Modal/ModalHeader.mjs","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@mantine/core/esm/components/Modal/ModalOverlay.mjs":{"*":{"id":"(rsc)/./node_modules/@mantine/core/esm/components/Modal/ModalOverlay.mjs","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@mantine/core/esm/components/Modal/ModalRoot.mjs":{"*":{"id":"(rsc)/./node_modules/@mantine/core/esm/components/Modal/ModalRoot.mjs","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@mantine/core/esm/components/Modal/ModalStack.mjs":{"*":{"id":"(rsc)/./node_modules/@mantine/core/esm/components/Modal/ModalStack.mjs","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@mantine/core/esm/components/Modal/ModalTitle.mjs":{"*":{"id":"(rsc)/./node_modules/@mantine/core/esm/components/Modal/ModalTitle.mjs","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@mantine/core/esm/components/Modal/use-modals-stack.mjs":{"*":{"id":"(rsc)/./node_modules/@mantine/core/esm/components/Modal/use-modals-stack.mjs","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@mantine/core/esm/components/ModalBase/ModalBase.mjs":{"*":{"id":"(rsc)/./node_modules/@mantine/core/esm/components/ModalBase/ModalBase.mjs","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@mantine/core/esm/components/ModalBase/ModalBaseBody.mjs":{"*":{"id":"(rsc)/./node_modules/@mantine/core/esm/components/ModalBase/ModalBaseBody.mjs","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@mantine/core/esm/components/ModalBase/ModalBaseCloseButton.mjs":{"*":{"id":"(rsc)/./node_modules/@mantine/core/esm/components/ModalBase/ModalBaseCloseButton.mjs","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@mantine/core/esm/components/ModalBase/ModalBaseContent.mjs":{"*":{"id":"(rsc)/./node_modules/@mantine/core/esm/components/ModalBase/ModalBaseContent.mjs","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@mantine/core/esm/components/ModalBase/ModalBaseHeader.mjs":{"*":{"id":"(rsc)/./node_modules/@mantine/core/esm/components/ModalBase/ModalBaseHeader.mjs","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@mantine/core/esm/components/ModalBase/ModalBaseOverlay.mjs":{"*":{"id":"(rsc)/./node_modules/@mantine/core/esm/components/ModalBase/ModalBaseOverlay.mjs","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@mantine/core/esm/components/ModalBase/ModalBaseTitle.mjs":{"*":{"id":"(rsc)/./node_modules/@mantine/core/esm/components/ModalBase/ModalBaseTitle.mjs","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@mantine/core/esm/components/ModalBase/NativeScrollArea.mjs":{"*":{"id":"(rsc)/./node_modules/@mantine/core/esm/components/ModalBase/NativeScrollArea.mjs","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@mantine/core/esm/components/MultiSelect/MultiSelect.mjs":{"*":{"id":"(rsc)/./node_modules/@mantine/core/esm/components/MultiSelect/MultiSelect.mjs","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@mantine/core/esm/components/NativeSelect/NativeSelect.mjs":{"*":{"id":"(rsc)/./node_modules/@mantine/core/esm/components/NativeSelect/NativeSelect.mjs","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@mantine/core/esm/components/NavLink/NavLink.mjs":{"*":{"id":"(rsc)/./node_modules/@mantine/core/esm/components/NavLink/NavLink.mjs","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@mantine/core/esm/components/Notification/Notification.mjs":{"*":{"id":"(rsc)/./node_modules/@mantine/core/esm/components/Notification/Notification.mjs","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@mantine/core/esm/components/NumberFormatter/NumberFormatter.mjs":{"*":{"id":"(rsc)/./node_modules/@mantine/core/esm/components/NumberFormatter/NumberFormatter.mjs","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@mantine/core/esm/components/NumberInput/NumberInput.mjs":{"*":{"id":"(rsc)/./node_modules/@mantine/core/esm/components/NumberInput/NumberInput.mjs","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@mantine/core/esm/components/Overlay/Overlay.mjs":{"*":{"id":"(rsc)/./node_modules/@mantine/core/esm/components/Overlay/Overlay.mjs","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@mantine/core/esm/components/Pagination/Pagination.mjs":{"*":{"id":"(rsc)/./node_modules/@mantine/core/esm/components/Pagination/Pagination.mjs","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@mantine/core/esm/components/Pagination/PaginationControl/PaginationControl.mjs":{"*":{"id":"(rsc)/./node_modules/@mantine/core/esm/components/Pagination/PaginationControl/PaginationControl.mjs","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@mantine/core/esm/components/Pagination/PaginationDots/PaginationDots.mjs":{"*":{"id":"(rsc)/./node_modules/@mantine/core/esm/components/Pagination/PaginationDots/PaginationDots.mjs","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@mantine/core/esm/components/Pagination/PaginationEdges/PaginationEdges.mjs":{"*":{"id":"(rsc)/./node_modules/@mantine/core/esm/components/Pagination/PaginationEdges/PaginationEdges.mjs","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@mantine/core/esm/components/Pagination/PaginationItems/PaginationItems.mjs":{"*":{"id":"(rsc)/./node_modules/@mantine/core/esm/components/Pagination/PaginationItems/PaginationItems.mjs","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@mantine/core/esm/components/Pagination/PaginationRoot/PaginationRoot.mjs":{"*":{"id":"(rsc)/./node_modules/@mantine/core/esm/components/Pagination/PaginationRoot/PaginationRoot.mjs","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@mantine/core/esm/components/Paper/Paper.mjs":{"*":{"id":"(rsc)/./node_modules/@mantine/core/esm/components/Paper/Paper.mjs","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@mantine/core/esm/components/PasswordInput/PasswordInput.mjs":{"*":{"id":"(rsc)/./node_modules/@mantine/core/esm/components/PasswordInput/PasswordInput.mjs","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@mantine/core/esm/components/Pill/Pill.mjs":{"*":{"id":"(rsc)/./node_modules/@mantine/core/esm/components/Pill/Pill.mjs","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@mantine/core/esm/components/Pill/PillGroup/PillGroup.mjs":{"*":{"id":"(rsc)/./node_modules/@mantine/core/esm/components/Pill/PillGroup/PillGroup.mjs","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@mantine/core/esm/components/PillsInput/PillsInput.mjs":{"*":{"id":"(rsc)/./node_modules/@mantine/core/esm/components/PillsInput/PillsInput.mjs","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@mantine/core/esm/components/PillsInput/PillsInputField/PillsInputField.mjs":{"*":{"id":"(rsc)/./node_modules/@mantine/core/esm/components/PillsInput/PillsInputField/PillsInputField.mjs","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@mantine/core/esm/components/PinInput/PinInput.mjs":{"*":{"id":"(rsc)/./node_modules/@mantine/core/esm/components/PinInput/PinInput.mjs","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@mantine/core/esm/components/Popover/Popover.mjs":{"*":{"id":"(rsc)/./node_modules/@mantine/core/esm/components/Popover/Popover.mjs","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@mantine/core/esm/components/Popover/PopoverDropdown/PopoverDropdown.mjs":{"*":{"id":"(rsc)/./node_modules/@mantine/core/esm/components/Popover/PopoverDropdown/PopoverDropdown.mjs","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@mantine/core/esm/components/Popover/PopoverTarget/PopoverTarget.mjs":{"*":{"id":"(rsc)/./node_modules/@mantine/core/esm/components/Popover/PopoverTarget/PopoverTarget.mjs","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@mantine/core/esm/components/Portal/OptionalPortal.mjs":{"*":{"id":"(rsc)/./node_modules/@mantine/core/esm/components/Portal/OptionalPortal.mjs","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@mantine/core/esm/components/Portal/Portal.mjs":{"*":{"id":"(rsc)/./node_modules/@mantine/core/esm/components/Portal/Portal.mjs","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@mantine/core/esm/components/Progress/Progress.mjs":{"*":{"id":"(rsc)/./node_modules/@mantine/core/esm/components/Progress/Progress.mjs","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@mantine/core/esm/components/Progress/ProgressLabel/ProgressLabel.mjs":{"*":{"id":"(rsc)/./node_modules/@mantine/core/esm/components/Progress/ProgressLabel/ProgressLabel.mjs","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@mantine/core/esm/components/Progress/ProgressRoot/ProgressRoot.mjs":{"*":{"id":"(rsc)/./node_modules/@mantine/core/esm/components/Progress/ProgressRoot/ProgressRoot.mjs","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@mantine/core/esm/components/Progress/ProgressSection/ProgressSection.mjs":{"*":{"id":"(rsc)/./node_modules/@mantine/core/esm/components/Progress/ProgressSection/ProgressSection.mjs","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@mantine/core/esm/components/Radio/Radio.mjs":{"*":{"id":"(rsc)/./node_modules/@mantine/core/esm/components/Radio/Radio.mjs","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@mantine/core/esm/components/Radio/RadioCard/RadioCard.context.mjs":{"*":{"id":"(rsc)/./node_modules/@mantine/core/esm/components/Radio/RadioCard/RadioCard.context.mjs","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@mantine/core/esm/components/Radio/RadioCard/RadioCard.mjs":{"*":{"id":"(rsc)/./node_modules/@mantine/core/esm/components/Radio/RadioCard/RadioCard.mjs","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@mantine/core/esm/components/Radio/RadioGroup/RadioGroup.mjs":{"*":{"id":"(rsc)/./node_modules/@mantine/core/esm/components/Radio/RadioGroup/RadioGroup.mjs","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@mantine/core/esm/components/Radio/RadioIcon.mjs":{"*":{"id":"(rsc)/./node_modules/@mantine/core/esm/components/Radio/RadioIcon.mjs","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@mantine/core/esm/components/Radio/RadioIndicator/RadioIndicator.mjs":{"*":{"id":"(rsc)/./node_modules/@mantine/core/esm/components/Radio/RadioIndicator/RadioIndicator.mjs","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@mantine/core/esm/components/Rating/Rating.mjs":{"*":{"id":"(rsc)/./node_modules/@mantine/core/esm/components/Rating/Rating.mjs","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@mantine/core/esm/components/RingProgress/RingProgress.mjs":{"*":{"id":"(rsc)/./node_modules/@mantine/core/esm/components/RingProgress/RingProgress.mjs","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@mantine/core/esm/components/ScrollArea/ScrollArea.mjs":{"*":{"id":"(rsc)/./node_modules/@mantine/core/esm/components/ScrollArea/ScrollArea.mjs","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@mantine/core/esm/components/SegmentedControl/SegmentedControl.mjs":{"*":{"id":"(rsc)/./node_modules/@mantine/core/esm/components/SegmentedControl/SegmentedControl.mjs","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@mantine/core/esm/components/Select/Select.mjs":{"*":{"id":"(rsc)/./node_modules/@mantine/core/esm/components/Select/Select.mjs","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@mantine/core/esm/components/SemiCircleProgress/SemiCircleProgress.mjs":{"*":{"id":"(rsc)/./node_modules/@mantine/core/esm/components/SemiCircleProgress/SemiCircleProgress.mjs","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@mantine/core/esm/components/SimpleGrid/SimpleGrid.mjs":{"*":{"id":"(rsc)/./node_modules/@mantine/core/esm/components/SimpleGrid/SimpleGrid.mjs","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@mantine/core/esm/components/Skeleton/Skeleton.mjs":{"*":{"id":"(rsc)/./node_modules/@mantine/core/esm/components/Skeleton/Skeleton.mjs","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@mantine/core/esm/components/Slider/RangeSlider/RangeSlider.mjs":{"*":{"id":"(rsc)/./node_modules/@mantine/core/esm/components/Slider/RangeSlider/RangeSlider.mjs","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@mantine/core/esm/components/Slider/Slider/Slider.mjs":{"*":{"id":"(rsc)/./node_modules/@mantine/core/esm/components/Slider/Slider/Slider.mjs","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@mantine/core/esm/components/Space/Space.mjs":{"*":{"id":"(rsc)/./node_modules/@mantine/core/esm/components/Space/Space.mjs","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@mantine/core/esm/components/Spoiler/Spoiler.mjs":{"*":{"id":"(rsc)/./node_modules/@mantine/core/esm/components/Spoiler/Spoiler.mjs","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@mantine/core/esm/components/Stack/Stack.mjs":{"*":{"id":"(rsc)/./node_modules/@mantine/core/esm/components/Stack/Stack.mjs","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@mantine/core/esm/components/Stepper/Stepper.mjs":{"*":{"id":"(rsc)/./node_modules/@mantine/core/esm/components/Stepper/Stepper.mjs","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@mantine/core/esm/components/Stepper/StepperCompleted/StepperCompleted.mjs":{"*":{"id":"(rsc)/./node_modules/@mantine/core/esm/components/Stepper/StepperCompleted/StepperCompleted.mjs","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@mantine/core/esm/components/Stepper/StepperStep/StepperStep.mjs":{"*":{"id":"(rsc)/./node_modules/@mantine/core/esm/components/Stepper/StepperStep/StepperStep.mjs","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@mantine/core/esm/components/Switch/Switch.mjs":{"*":{"id":"(rsc)/./node_modules/@mantine/core/esm/components/Switch/Switch.mjs","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@mantine/core/esm/components/Switch/SwitchGroup/SwitchGroup.mjs":{"*":{"id":"(rsc)/./node_modules/@mantine/core/esm/components/Switch/SwitchGroup/SwitchGroup.mjs","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@mantine/core/esm/components/Table/Table.components.mjs":{"*":{"id":"(rsc)/./node_modules/@mantine/core/esm/components/Table/Table.components.mjs","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@mantine/core/esm/components/Table/Table.mjs":{"*":{"id":"(rsc)/./node_modules/@mantine/core/esm/components/Table/Table.mjs","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@mantine/core/esm/components/Table/TableScrollContainer.mjs":{"*":{"id":"(rsc)/./node_modules/@mantine/core/esm/components/Table/TableScrollContainer.mjs","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@mantine/core/esm/components/TableOfContents/TableOfContents.mjs":{"*":{"id":"(rsc)/./node_modules/@mantine/core/esm/components/TableOfContents/TableOfContents.mjs","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@mantine/core/esm/components/Tabs/Tabs.mjs":{"*":{"id":"(rsc)/./node_modules/@mantine/core/esm/components/Tabs/Tabs.mjs","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@mantine/core/esm/components/Tabs/TabsList/TabsList.mjs":{"*":{"id":"(rsc)/./node_modules/@mantine/core/esm/components/Tabs/TabsList/TabsList.mjs","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@mantine/core/esm/components/Tabs/TabsPanel/TabsPanel.mjs":{"*":{"id":"(rsc)/./node_modules/@mantine/core/esm/components/Tabs/TabsPanel/TabsPanel.mjs","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@mantine/core/esm/components/Tabs/TabsTab/TabsTab.mjs":{"*":{"id":"(rsc)/./node_modules/@mantine/core/esm/components/Tabs/TabsTab/TabsTab.mjs","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@mantine/core/esm/components/TagsInput/TagsInput.mjs":{"*":{"id":"(rsc)/./node_modules/@mantine/core/esm/components/TagsInput/TagsInput.mjs","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@mantine/core/esm/components/Text/Text.mjs":{"*":{"id":"(rsc)/./node_modules/@mantine/core/esm/components/Text/Text.mjs","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@mantine/core/esm/components/Textarea/Textarea.mjs":{"*":{"id":"(rsc)/./node_modules/@mantine/core/esm/components/Textarea/Textarea.mjs","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@mantine/core/esm/components/TextInput/TextInput.mjs":{"*":{"id":"(rsc)/./node_modules/@mantine/core/esm/components/TextInput/TextInput.mjs","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@mantine/core/esm/components/ThemeIcon/ThemeIcon.mjs":{"*":{"id":"(rsc)/./node_modules/@mantine/core/esm/components/ThemeIcon/ThemeIcon.mjs","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@mantine/core/esm/components/Timeline/Timeline.mjs":{"*":{"id":"(rsc)/./node_modules/@mantine/core/esm/components/Timeline/Timeline.mjs","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@mantine/core/esm/components/Timeline/TimelineItem/TimelineItem.mjs":{"*":{"id":"(rsc)/./node_modules/@mantine/core/esm/components/Timeline/TimelineItem/TimelineItem.mjs","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@mantine/core/esm/components/Title/Title.mjs":{"*":{"id":"(rsc)/./node_modules/@mantine/core/esm/components/Title/Title.mjs","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@mantine/core/esm/components/Tooltip/Tooltip.mjs":{"*":{"id":"(rsc)/./node_modules/@mantine/core/esm/components/Tooltip/Tooltip.mjs","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@mantine/core/esm/components/Tooltip/TooltipFloating/TooltipFloating.mjs":{"*":{"id":"(rsc)/./node_modules/@mantine/core/esm/components/Tooltip/TooltipFloating/TooltipFloating.mjs","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@mantine/core/esm/components/Tooltip/TooltipGroup/TooltipGroup.mjs":{"*":{"id":"(rsc)/./node_modules/@mantine/core/esm/components/Tooltip/TooltipGroup/TooltipGroup.mjs","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@mantine/core/esm/components/Transition/get-transition-props/get-transition-props.mjs":{"*":{"id":"(rsc)/./node_modules/@mantine/core/esm/components/Transition/get-transition-props/get-transition-props.mjs","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@mantine/core/esm/components/Transition/Transition.mjs":{"*":{"id":"(rsc)/./node_modules/@mantine/core/esm/components/Transition/Transition.mjs","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@mantine/core/esm/components/Transition/transitions.mjs":{"*":{"id":"(rsc)/./node_modules/@mantine/core/esm/components/Transition/transitions.mjs","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@mantine/core/esm/components/Tree/Tree.mjs":{"*":{"id":"(rsc)/./node_modules/@mantine/core/esm/components/Tree/Tree.mjs","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@mantine/core/esm/components/Tree/use-tree.mjs":{"*":{"id":"(rsc)/./node_modules/@mantine/core/esm/components/Tree/use-tree.mjs","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@mantine/core/esm/components/TypographyStylesProvider/TypographyStylesProvider.mjs":{"*":{"id":"(rsc)/./node_modules/@mantine/core/esm/components/TypographyStylesProvider/TypographyStylesProvider.mjs","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@mantine/core/esm/components/UnstyledButton/UnstyledButton.mjs":{"*":{"id":"(rsc)/./node_modules/@mantine/core/esm/components/UnstyledButton/UnstyledButton.mjs","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@mantine/core/esm/components/VisuallyHidden/VisuallyHidden.mjs":{"*":{"id":"(rsc)/./node_modules/@mantine/core/esm/components/VisuallyHidden/VisuallyHidden.mjs","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@mantine/core/esm/core/Box/Box.mjs":{"*":{"id":"(rsc)/./node_modules/@mantine/core/esm/core/Box/Box.mjs","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@mantine/core/esm/core/Box/get-style-object/get-style-object.mjs":{"*":{"id":"(rsc)/./node_modules/@mantine/core/esm/core/Box/get-style-object/get-style-object.mjs","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@mantine/core/esm/core/Box/style-props/extract-style-props/extract-style-props.mjs":{"*":{"id":"(rsc)/./node_modules/@mantine/core/esm/core/Box/style-props/extract-style-props/extract-style-props.mjs","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@mantine/core/esm/core/Box/style-props/parse-style-props/parse-style-props.mjs":{"*":{"id":"(rsc)/./node_modules/@mantine/core/esm/core/Box/style-props/parse-style-props/parse-style-props.mjs","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@mantine/core/esm/core/Box/style-props/style-props-data.mjs":{"*":{"id":"(rsc)/./node_modules/@mantine/core/esm/core/Box/style-props/style-props-data.mjs","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@mantine/core/esm/core/Box/use-random-classname/use-random-classname.mjs":{"*":{"id":"(rsc)/./node_modules/@mantine/core/esm/core/Box/use-random-classname/use-random-classname.mjs","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@mantine/core/esm/core/DirectionProvider/DirectionProvider.mjs":{"*":{"id":"(rsc)/./node_modules/@mantine/core/esm/core/DirectionProvider/DirectionProvider.mjs","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@mantine/core/esm/core/factory/factory.mjs":{"*":{"id":"(rsc)/./node_modules/@mantine/core/esm/core/factory/factory.mjs","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@mantine/core/esm/core/factory/polymorphic-factory.mjs":{"*":{"id":"(rsc)/./node_modules/@mantine/core/esm/core/factory/polymorphic-factory.mjs","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@mantine/core/esm/core/InlineStyles/InlineStyles.mjs":{"*":{"id":"(rsc)/./node_modules/@mantine/core/esm/core/InlineStyles/InlineStyles.mjs","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@mantine/core/esm/core/InlineStyles/styles-to-string/styles-to-string.mjs":{"*":{"id":"(rsc)/./node_modules/@mantine/core/esm/core/InlineStyles/styles-to-string/styles-to-string.mjs","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@mantine/core/esm/core/MantineProvider/color-functions/default-variant-colors-resolver/default-variant-colors-resolver.mjs":{"*":{"id":"(rsc)/./node_modules/@mantine/core/esm/core/MantineProvider/color-functions/default-variant-colors-resolver/default-variant-colors-resolver.mjs","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@mantine/core/esm/core/MantineProvider/color-functions/get-auto-contrast-value/get-auto-contrast-value.mjs":{"*":{"id":"(rsc)/./node_modules/@mantine/core/esm/core/MantineProvider/color-functions/get-auto-contrast-value/get-auto-contrast-value.mjs","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@mantine/core/esm/core/MantineProvider/color-functions/get-contrast-color/get-contrast-color.mjs":{"*":{"id":"(rsc)/./node_modules/@mantine/core/esm/core/MantineProvider/color-functions/get-contrast-color/get-contrast-color.mjs","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@mantine/core/esm/core/MantineProvider/color-functions/get-gradient/get-gradient.mjs":{"*":{"id":"(rsc)/./node_modules/@mantine/core/esm/core/MantineProvider/color-functions/get-gradient/get-gradient.mjs","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@mantine/core/esm/core/MantineProvider/color-functions/get-primary-shade/get-primary-shade.mjs":{"*":{"id":"(rsc)/./node_modules/@mantine/core/esm/core/MantineProvider/color-functions/get-primary-shade/get-primary-shade.mjs","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@mantine/core/esm/core/MantineProvider/color-functions/get-theme-color/get-theme-color.mjs":{"*":{"id":"(rsc)/./node_modules/@mantine/core/esm/core/MantineProvider/color-functions/get-theme-color/get-theme-color.mjs","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@mantine/core/esm/core/MantineProvider/color-functions/parse-theme-color/parse-theme-color.mjs":{"*":{"id":"(rsc)/./node_modules/@mantine/core/esm/core/MantineProvider/color-functions/parse-theme-color/parse-theme-color.mjs","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@mantine/core/esm/core/MantineProvider/color-scheme-managers/is-mantine-color-scheme.mjs":{"*":{"id":"(rsc)/./node_modules/@mantine/core/esm/core/MantineProvider/color-scheme-managers/is-mantine-color-scheme.mjs","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@mantine/core/esm/core/MantineProvider/color-scheme-managers/local-storage-manager.mjs":{"*":{"id":"(rsc)/./node_modules/@mantine/core/esm/core/MantineProvider/color-scheme-managers/local-storage-manager.mjs","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@mantine/core/esm/core/MantineProvider/ColorSchemeScript/ColorSchemeScript.mjs":{"*":{"id":"(rsc)/./node_modules/@mantine/core/esm/core/MantineProvider/ColorSchemeScript/ColorSchemeScript.mjs","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@mantine/core/esm/core/MantineProvider/convert-css-variables/convert-css-variables.mjs":{"*":{"id":"(rsc)/./node_modules/@mantine/core/esm/core/MantineProvider/convert-css-variables/convert-css-variables.mjs","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@mantine/core/esm/core/MantineProvider/Mantine.context.mjs":{"*":{"id":"(rsc)/./node_modules/@mantine/core/esm/core/MantineProvider/Mantine.context.mjs","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@mantine/core/esm/core/MantineProvider/MantineCssVariables/default-css-variables-resolver.mjs":{"*":{"id":"(rsc)/./node_modules/@mantine/core/esm/core/MantineProvider/MantineCssVariables/default-css-variables-resolver.mjs","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@mantine/core/esm/core/MantineProvider/MantineCssVariables/get-css-color-variables.mjs":{"*":{"id":"(rsc)/./node_modules/@mantine/core/esm/core/MantineProvider/MantineCssVariables/get-css-color-variables.mjs","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@mantine/core/esm/core/MantineProvider/MantineCssVariables/MantineCssVariables.mjs":{"*":{"id":"(rsc)/./node_modules/@mantine/core/esm/core/MantineProvider/MantineCssVariables/MantineCssVariables.mjs","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@mantine/core/esm/core/MantineProvider/MantineProvider.mjs":{"*":{"id":"(rsc)/./node_modules/@mantine/core/esm/core/MantineProvider/MantineProvider.mjs","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@mantine/core/esm/core/MantineProvider/MantineThemeProvider/MantineThemeProvider.mjs":{"*":{"id":"(rsc)/./node_modules/@mantine/core/esm/core/MantineProvider/MantineThemeProvider/MantineThemeProvider.mjs","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@mantine/core/esm/core/MantineProvider/merge-theme-overrides/merge-theme-overrides.mjs":{"*":{"id":"(rsc)/./node_modules/@mantine/core/esm/core/MantineProvider/merge-theme-overrides/merge-theme-overrides.mjs","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@mantine/core/esm/core/MantineProvider/use-mantine-color-scheme/use-computed-color-scheme.mjs":{"*":{"id":"(rsc)/./node_modules/@mantine/core/esm/core/MantineProvider/use-mantine-color-scheme/use-computed-color-scheme.mjs","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@mantine/core/esm/core/MantineProvider/use-mantine-color-scheme/use-mantine-color-scheme.mjs":{"*":{"id":"(rsc)/./node_modules/@mantine/core/esm/core/MantineProvider/use-mantine-color-scheme/use-mantine-color-scheme.mjs","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@mantine/core/esm/core/MantineProvider/use-mantine-color-scheme/use-provider-color-scheme.mjs":{"*":{"id":"(rsc)/./node_modules/@mantine/core/esm/core/MantineProvider/use-mantine-color-scheme/use-provider-color-scheme.mjs","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@mantine/core/esm/core/MantineProvider/use-matches/use-matches.mjs":{"*":{"id":"(rsc)/./node_modules/@mantine/core/esm/core/MantineProvider/use-matches/use-matches.mjs","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@mantine/core/esm/core/MantineProvider/use-props/use-props.mjs":{"*":{"id":"(rsc)/./node_modules/@mantine/core/esm/core/MantineProvider/use-props/use-props.mjs","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@mantine/core/esm/core/styles-api/create-vars-resolver/create-vars-resolver.mjs":{"*":{"id":"(rsc)/./node_modules/@mantine/core/esm/core/styles-api/create-vars-resolver/create-vars-resolver.mjs","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@mantine/core/esm/core/styles-api/use-resolved-styles-api/use-resolved-styles-api.mjs":{"*":{"id":"(rsc)/./node_modules/@mantine/core/esm/core/styles-api/use-resolved-styles-api/use-resolved-styles-api.mjs","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@mantine/core/esm/core/styles-api/use-styles/get-class-name/get-global-class-names/get-global-class-names.mjs":{"*":{"id":"(rsc)/./node_modules/@mantine/core/esm/core/styles-api/use-styles/get-class-name/get-global-class-names/get-global-class-names.mjs","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@mantine/core/esm/core/styles-api/use-styles/get-class-name/resolve-class-names/resolve-class-names.mjs":{"*":{"id":"(rsc)/./node_modules/@mantine/core/esm/core/styles-api/use-styles/get-class-name/resolve-class-names/resolve-class-names.mjs","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@mantine/core/esm/core/styles-api/use-styles/get-style/resolve-styles/resolve-styles.mjs":{"*":{"id":"(rsc)/./node_modules/@mantine/core/esm/core/styles-api/use-styles/get-style/resolve-styles/resolve-styles.mjs","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@mantine/core/esm/core/styles-api/use-styles/use-styles.mjs":{"*":{"id":"(rsc)/./node_modules/@mantine/core/esm/core/styles-api/use-styles/use-styles.mjs","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@mantine/core/esm/core/utils/camel-to-kebab-case/camel-to-kebab-case.mjs":{"*":{"id":"(rsc)/./node_modules/@mantine/core/esm/core/utils/camel-to-kebab-case/camel-to-kebab-case.mjs","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@mantine/core/esm/core/utils/close-on-escape/close-on-escape.mjs":{"*":{"id":"(rsc)/./node_modules/@mantine/core/esm/core/utils/close-on-escape/close-on-escape.mjs","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@mantine/core/esm/core/utils/create-event-handler/create-event-handler.mjs":{"*":{"id":"(rsc)/./node_modules/@mantine/core/esm/core/utils/create-event-handler/create-event-handler.mjs","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@mantine/core/esm/core/utils/create-optional-context/create-optional-context.mjs":{"*":{"id":"(rsc)/./node_modules/@mantine/core/esm/core/utils/create-optional-context/create-optional-context.mjs","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@mantine/core/esm/core/utils/create-safe-context/create-safe-context.mjs":{"*":{"id":"(rsc)/./node_modules/@mantine/core/esm/core/utils/create-safe-context/create-safe-context.mjs","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@mantine/core/esm/core/utils/create-scoped-keydown-handler/create-scoped-keydown-handler.mjs":{"*":{"id":"(rsc)/./node_modules/@mantine/core/esm/core/utils/create-scoped-keydown-handler/create-scoped-keydown-handler.mjs","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@mantine/core/esm/core/utils/create-use-external-events/create-use-external-events.mjs":{"*":{"id":"(rsc)/./node_modules/@mantine/core/esm/core/utils/create-use-external-events/create-use-external-events.mjs","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@mantine/core/esm/core/utils/filter-props/filter-props.mjs":{"*":{"id":"(rsc)/./node_modules/@mantine/core/esm/core/utils/filter-props/filter-props.mjs","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@mantine/core/esm/core/utils/find-closest-number/find-closest-number.mjs":{"*":{"id":"(rsc)/./node_modules/@mantine/core/esm/core/utils/find-closest-number/find-closest-number.mjs","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@mantine/core/esm/core/utils/find-element-ancestor/find-element-ancestor.mjs":{"*":{"id":"(rsc)/./node_modules/@mantine/core/esm/core/utils/find-element-ancestor/find-element-ancestor.mjs","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@mantine/core/esm/core/utils/get-base-value/get-base-value.mjs":{"*":{"id":"(rsc)/./node_modules/@mantine/core/esm/core/utils/get-base-value/get-base-value.mjs","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@mantine/core/esm/core/utils/get-breakpoint-value/get-breakpoint-value.mjs":{"*":{"id":"(rsc)/./node_modules/@mantine/core/esm/core/utils/get-breakpoint-value/get-breakpoint-value.mjs","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@mantine/core/esm/core/utils/get-context-item-index/get-context-item-index.mjs":{"*":{"id":"(rsc)/./node_modules/@mantine/core/esm/core/utils/get-context-item-index/get-context-item-index.mjs","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@mantine/core/esm/core/utils/get-default-z-index/get-default-z-index.mjs":{"*":{"id":"(rsc)/./node_modules/@mantine/core/esm/core/utils/get-default-z-index/get-default-z-index.mjs","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@mantine/core/esm/core/utils/get-env/get-env.mjs":{"*":{"id":"(rsc)/./node_modules/@mantine/core/esm/core/utils/get-env/get-env.mjs","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@mantine/core/esm/core/utils/get-ref-prop/get-ref-prop.mjs":{"*":{"id":"(rsc)/./node_modules/@mantine/core/esm/core/utils/get-ref-prop/get-ref-prop.mjs","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@mantine/core/esm/core/utils/get-safe-id/get-safe-id.mjs":{"*":{"id":"(rsc)/./node_modules/@mantine/core/esm/core/utils/get-safe-id/get-safe-id.mjs","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@mantine/core/esm/core/utils/get-size/get-size.mjs":{"*":{"id":"(rsc)/./node_modules/@mantine/core/esm/core/utils/get-size/get-size.mjs","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@mantine/core/esm/core/utils/get-sorted-breakpoints/get-sorted-breakpoints.mjs":{"*":{"id":"(rsc)/./node_modules/@mantine/core/esm/core/utils/get-sorted-breakpoints/get-sorted-breakpoints.mjs","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@mantine/core/esm/core/utils/is-element/is-element.mjs":{"*":{"id":"(rsc)/./node_modules/@mantine/core/esm/core/utils/is-element/is-element.mjs","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@mantine/core/esm/core/utils/is-number-like/is-number-like.mjs":{"*":{"id":"(rsc)/./node_modules/@mantine/core/esm/core/utils/is-number-like/is-number-like.mjs","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@mantine/core/esm/core/utils/keys/keys.mjs":{"*":{"id":"(rsc)/./node_modules/@mantine/core/esm/core/utils/keys/keys.mjs","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@mantine/core/esm/core/utils/memoize/memoize.mjs":{"*":{"id":"(rsc)/./node_modules/@mantine/core/esm/core/utils/memoize/memoize.mjs","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@mantine/core/esm/core/utils/noop/noop.mjs":{"*":{"id":"(rsc)/./node_modules/@mantine/core/esm/core/utils/noop/noop.mjs","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@mantine/core/esm/core/utils/use-hovered/use-hovered.mjs":{"*":{"id":"(rsc)/./node_modules/@mantine/core/esm/core/utils/use-hovered/use-hovered.mjs","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@mantine/core/styles.css":{"*":{"id":"(rsc)/./node_modules/@mantine/core/styles.css","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@mantine/hooks/esm/use-callback-ref/use-callback-ref.mjs":{"*":{"id":"(rsc)/./node_modules/@mantine/hooks/esm/use-callback-ref/use-callback-ref.mjs","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@mantine/hooks/esm/use-click-outside/use-click-outside.mjs":{"*":{"id":"(rsc)/./node_modules/@mantine/hooks/esm/use-click-outside/use-click-outside.mjs","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@mantine/hooks/esm/use-clipboard/use-clipboard.mjs":{"*":{"id":"(rsc)/./node_modules/@mantine/hooks/esm/use-clipboard/use-clipboard.mjs","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@mantine/hooks/esm/use-color-scheme/use-color-scheme.mjs":{"*":{"id":"(rsc)/./node_modules/@mantine/hooks/esm/use-color-scheme/use-color-scheme.mjs","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@mantine/hooks/esm/use-counter/use-counter.mjs":{"*":{"id":"(rsc)/./node_modules/@mantine/hooks/esm/use-counter/use-counter.mjs","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@mantine/hooks/esm/use-debounced-callback/use-debounced-callback.mjs":{"*":{"id":"(rsc)/./node_modules/@mantine/hooks/esm/use-debounced-callback/use-debounced-callback.mjs","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@mantine/hooks/esm/use-debounced-state/use-debounced-state.mjs":{"*":{"id":"(rsc)/./node_modules/@mantine/hooks/esm/use-debounced-state/use-debounced-state.mjs","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@mantine/hooks/esm/use-debounced-value/use-debounced-value.mjs":{"*":{"id":"(rsc)/./node_modules/@mantine/hooks/esm/use-debounced-value/use-debounced-value.mjs","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@mantine/hooks/esm/use-did-update/use-did-update.mjs":{"*":{"id":"(rsc)/./node_modules/@mantine/hooks/esm/use-did-update/use-did-update.mjs","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@mantine/hooks/esm/use-disclosure/use-disclosure.mjs":{"*":{"id":"(rsc)/./node_modules/@mantine/hooks/esm/use-disclosure/use-disclosure.mjs","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@mantine/hooks/esm/use-document-title/use-document-title.mjs":{"*":{"id":"(rsc)/./node_modules/@mantine/hooks/esm/use-document-title/use-document-title.mjs","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@mantine/hooks/esm/use-document-visibility/use-document-visibility.mjs":{"*":{"id":"(rsc)/./node_modules/@mantine/hooks/esm/use-document-visibility/use-document-visibility.mjs","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@mantine/hooks/esm/use-event-listener/use-event-listener.mjs":{"*":{"id":"(rsc)/./node_modules/@mantine/hooks/esm/use-event-listener/use-event-listener.mjs","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@mantine/hooks/esm/use-eye-dropper/use-eye-dropper.mjs":{"*":{"id":"(rsc)/./node_modules/@mantine/hooks/esm/use-eye-dropper/use-eye-dropper.mjs","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@mantine/hooks/esm/use-favicon/use-favicon.mjs":{"*":{"id":"(rsc)/./node_modules/@mantine/hooks/esm/use-favicon/use-favicon.mjs","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@mantine/hooks/esm/use-fetch/use-fetch.mjs":{"*":{"id":"(rsc)/./node_modules/@mantine/hooks/esm/use-fetch/use-fetch.mjs","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@mantine/hooks/esm/use-file-dialog/use-file-dialog.mjs":{"*":{"id":"(rsc)/./node_modules/@mantine/hooks/esm/use-file-dialog/use-file-dialog.mjs","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@mantine/hooks/esm/use-focus-return/use-focus-return.mjs":{"*":{"id":"(rsc)/./node_modules/@mantine/hooks/esm/use-focus-return/use-focus-return.mjs","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@mantine/hooks/esm/use-focus-trap/use-focus-trap.mjs":{"*":{"id":"(rsc)/./node_modules/@mantine/hooks/esm/use-focus-trap/use-focus-trap.mjs","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@mantine/hooks/esm/use-focus-within/use-focus-within.mjs":{"*":{"id":"(rsc)/./node_modules/@mantine/hooks/esm/use-focus-within/use-focus-within.mjs","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@mantine/hooks/esm/use-force-update/use-force-update.mjs":{"*":{"id":"(rsc)/./node_modules/@mantine/hooks/esm/use-force-update/use-force-update.mjs","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@mantine/hooks/esm/use-fullscreen/use-fullscreen.mjs":{"*":{"id":"(rsc)/./node_modules/@mantine/hooks/esm/use-fullscreen/use-fullscreen.mjs","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@mantine/hooks/esm/use-hash/use-hash.mjs":{"*":{"id":"(rsc)/./node_modules/@mantine/hooks/esm/use-hash/use-hash.mjs","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@mantine/hooks/esm/use-headroom/use-headroom.mjs":{"*":{"id":"(rsc)/./node_modules/@mantine/hooks/esm/use-headroom/use-headroom.mjs","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@mantine/hooks/esm/use-hotkeys/parse-hotkey.mjs":{"*":{"id":"(rsc)/./node_modules/@mantine/hooks/esm/use-hotkeys/parse-hotkey.mjs","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@mantine/hooks/esm/use-hotkeys/use-hotkeys.mjs":{"*":{"id":"(rsc)/./node_modules/@mantine/hooks/esm/use-hotkeys/use-hotkeys.mjs","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@mantine/hooks/esm/use-hover/use-hover.mjs":{"*":{"id":"(rsc)/./node_modules/@mantine/hooks/esm/use-hover/use-hover.mjs","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@mantine/hooks/esm/use-id/use-id.mjs":{"*":{"id":"(rsc)/./node_modules/@mantine/hooks/esm/use-id/use-id.mjs","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@mantine/hooks/esm/use-idle/use-idle.mjs":{"*":{"id":"(rsc)/./node_modules/@mantine/hooks/esm/use-idle/use-idle.mjs","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@mantine/hooks/esm/use-in-viewport/use-in-viewport.mjs":{"*":{"id":"(rsc)/./node_modules/@mantine/hooks/esm/use-in-viewport/use-in-viewport.mjs","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@mantine/hooks/esm/use-input-state/use-input-state.mjs":{"*":{"id":"(rsc)/./node_modules/@mantine/hooks/esm/use-input-state/use-input-state.mjs","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@mantine/hooks/esm/use-intersection/use-intersection.mjs":{"*":{"id":"(rsc)/./node_modules/@mantine/hooks/esm/use-intersection/use-intersection.mjs","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@mantine/hooks/esm/use-interval/use-interval.mjs":{"*":{"id":"(rsc)/./node_modules/@mantine/hooks/esm/use-interval/use-interval.mjs","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@mantine/hooks/esm/use-is-first-render/use-is-first-render.mjs":{"*":{"id":"(rsc)/./node_modules/@mantine/hooks/esm/use-is-first-render/use-is-first-render.mjs","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@mantine/hooks/esm/use-isomorphic-effect/use-isomorphic-effect.mjs":{"*":{"id":"(rsc)/./node_modules/@mantine/hooks/esm/use-isomorphic-effect/use-isomorphic-effect.mjs","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@mantine/hooks/esm/use-list-state/use-list-state.mjs":{"*":{"id":"(rsc)/./node_modules/@mantine/hooks/esm/use-list-state/use-list-state.mjs","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@mantine/hooks/esm/use-local-storage/use-local-storage.mjs":{"*":{"id":"(rsc)/./node_modules/@mantine/hooks/esm/use-local-storage/use-local-storage.mjs","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@mantine/hooks/esm/use-logger/use-logger.mjs":{"*":{"id":"(rsc)/./node_modules/@mantine/hooks/esm/use-logger/use-logger.mjs","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@mantine/hooks/esm/use-map/use-map.mjs":{"*":{"id":"(rsc)/./node_modules/@mantine/hooks/esm/use-map/use-map.mjs","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@mantine/hooks/esm/use-media-query/use-media-query.mjs":{"*":{"id":"(rsc)/./node_modules/@mantine/hooks/esm/use-media-query/use-media-query.mjs","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@mantine/hooks/esm/use-merged-ref/use-merged-ref.mjs":{"*":{"id":"(rsc)/./node_modules/@mantine/hooks/esm/use-merged-ref/use-merged-ref.mjs","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@mantine/hooks/esm/use-mounted/use-mounted.mjs":{"*":{"id":"(rsc)/./node_modules/@mantine/hooks/esm/use-mounted/use-mounted.mjs","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@mantine/hooks/esm/use-mouse/use-mouse.mjs":{"*":{"id":"(rsc)/./node_modules/@mantine/hooks/esm/use-mouse/use-mouse.mjs","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@mantine/hooks/esm/use-move/use-move.mjs":{"*":{"id":"(rsc)/./node_modules/@mantine/hooks/esm/use-move/use-move.mjs","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@mantine/hooks/esm/use-mutation-observer/use-mutation-observer.mjs":{"*":{"id":"(rsc)/./node_modules/@mantine/hooks/esm/use-mutation-observer/use-mutation-observer.mjs","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@mantine/hooks/esm/use-network/use-network.mjs":{"*":{"id":"(rsc)/./node_modules/@mantine/hooks/esm/use-network/use-network.mjs","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@mantine/hooks/esm/use-orientation/use-orientation.mjs":{"*":{"id":"(rsc)/./node_modules/@mantine/hooks/esm/use-orientation/use-orientation.mjs","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@mantine/hooks/esm/use-os/use-os.mjs":{"*":{"id":"(rsc)/./node_modules/@mantine/hooks/esm/use-os/use-os.mjs","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@mantine/hooks/esm/use-page-leave/use-page-leave.mjs":{"*":{"id":"(rsc)/./node_modules/@mantine/hooks/esm/use-page-leave/use-page-leave.mjs","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@mantine/hooks/esm/use-pagination/use-pagination.mjs":{"*":{"id":"(rsc)/./node_modules/@mantine/hooks/esm/use-pagination/use-pagination.mjs","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@mantine/hooks/esm/use-previous/use-previous.mjs":{"*":{"id":"(rsc)/./node_modules/@mantine/hooks/esm/use-previous/use-previous.mjs","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@mantine/hooks/esm/use-queue/use-queue.mjs":{"*":{"id":"(rsc)/./node_modules/@mantine/hooks/esm/use-queue/use-queue.mjs","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@mantine/hooks/esm/use-radial-move/use-radial-move.mjs":{"*":{"id":"(rsc)/./node_modules/@mantine/hooks/esm/use-radial-move/use-radial-move.mjs","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@mantine/hooks/esm/use-reduced-motion/use-reduced-motion.mjs":{"*":{"id":"(rsc)/./node_modules/@mantine/hooks/esm/use-reduced-motion/use-reduced-motion.mjs","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@mantine/hooks/esm/use-resize-observer/use-resize-observer.mjs":{"*":{"id":"(rsc)/./node_modules/@mantine/hooks/esm/use-resize-observer/use-resize-observer.mjs","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@mantine/hooks/esm/use-scroll-into-view/use-scroll-into-view.mjs":{"*":{"id":"(rsc)/./node_modules/@mantine/hooks/esm/use-scroll-into-view/use-scroll-into-view.mjs","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@mantine/hooks/esm/use-scroll-spy/use-scroll-spy.mjs":{"*":{"id":"(rsc)/./node_modules/@mantine/hooks/esm/use-scroll-spy/use-scroll-spy.mjs","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@mantine/hooks/esm/use-session-storage/use-session-storage.mjs":{"*":{"id":"(rsc)/./node_modules/@mantine/hooks/esm/use-session-storage/use-session-storage.mjs","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@mantine/hooks/esm/use-set-state/use-set-state.mjs":{"*":{"id":"(rsc)/./node_modules/@mantine/hooks/esm/use-set-state/use-set-state.mjs","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@mantine/hooks/esm/use-set/use-set.mjs":{"*":{"id":"(rsc)/./node_modules/@mantine/hooks/esm/use-set/use-set.mjs","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@mantine/hooks/esm/use-shallow-effect/use-shallow-effect.mjs":{"*":{"id":"(rsc)/./node_modules/@mantine/hooks/esm/use-shallow-effect/use-shallow-effect.mjs","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@mantine/hooks/esm/use-state-history/use-state-history.mjs":{"*":{"id":"(rsc)/./node_modules/@mantine/hooks/esm/use-state-history/use-state-history.mjs","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@mantine/hooks/esm/use-text-selection/use-text-selection.mjs":{"*":{"id":"(rsc)/./node_modules/@mantine/hooks/esm/use-text-selection/use-text-selection.mjs","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@mantine/hooks/esm/use-throttled-callback/use-throttled-callback.mjs":{"*":{"id":"(rsc)/./node_modules/@mantine/hooks/esm/use-throttled-callback/use-throttled-callback.mjs","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@mantine/hooks/esm/use-throttled-state/use-throttled-state.mjs":{"*":{"id":"(rsc)/./node_modules/@mantine/hooks/esm/use-throttled-state/use-throttled-state.mjs","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@mantine/hooks/esm/use-throttled-value/use-throttled-value.mjs":{"*":{"id":"(rsc)/./node_modules/@mantine/hooks/esm/use-throttled-value/use-throttled-value.mjs","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@mantine/hooks/esm/use-timeout/use-timeout.mjs":{"*":{"id":"(rsc)/./node_modules/@mantine/hooks/esm/use-timeout/use-timeout.mjs","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@mantine/hooks/esm/use-toggle/use-toggle.mjs":{"*":{"id":"(rsc)/./node_modules/@mantine/hooks/esm/use-toggle/use-toggle.mjs","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@mantine/hooks/esm/use-uncontrolled/use-uncontrolled.mjs":{"*":{"id":"(rsc)/./node_modules/@mantine/hooks/esm/use-uncontrolled/use-uncontrolled.mjs","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@mantine/hooks/esm/use-validated-state/use-validated-state.mjs":{"*":{"id":"(rsc)/./node_modules/@mantine/hooks/esm/use-validated-state/use-validated-state.mjs","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@mantine/hooks/esm/use-viewport-size/use-viewport-size.mjs":{"*":{"id":"(rsc)/./node_modules/@mantine/hooks/esm/use-viewport-size/use-viewport-size.mjs","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@mantine/hooks/esm/use-window-event/use-window-event.mjs":{"*":{"id":"(rsc)/./node_modules/@mantine/hooks/esm/use-window-event/use-window-event.mjs","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@mantine/hooks/esm/use-window-scroll/use-window-scroll.mjs":{"*":{"id":"(rsc)/./node_modules/@mantine/hooks/esm/use-window-scroll/use-window-scroll.mjs","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@mantine/hooks/esm/utils/clamp/clamp.mjs":{"*":{"id":"(rsc)/./node_modules/@mantine/hooks/esm/utils/clamp/clamp.mjs","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@mantine/hooks/esm/utils/lower-first/lower-first.mjs":{"*":{"id":"(rsc)/./node_modules/@mantine/hooks/esm/utils/lower-first/lower-first.mjs","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@mantine/hooks/esm/utils/random-id/random-id.mjs":{"*":{"id":"(rsc)/./node_modules/@mantine/hooks/esm/utils/random-id/random-id.mjs","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@mantine/hooks/esm/utils/range/range.mjs":{"*":{"id":"(rsc)/./node_modules/@mantine/hooks/esm/utils/range/range.mjs","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@mantine/hooks/esm/utils/shallow-equal/shallow-equal.mjs":{"*":{"id":"(rsc)/./node_modules/@mantine/hooks/esm/utils/shallow-equal/shallow-equal.mjs","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@mantine/hooks/esm/utils/upper-first/upper-first.mjs":{"*":{"id":"(rsc)/./node_modules/@mantine/hooks/esm/utils/upper-first/upper-first.mjs","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@mantine/modals/esm/events.mjs":{"*":{"id":"(rsc)/./node_modules/@mantine/modals/esm/events.mjs","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@mantine/modals/esm/ModalsProvider.mjs":{"*":{"id":"(rsc)/./node_modules/@mantine/modals/esm/ModalsProvider.mjs","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@mantine/modals/esm/use-modals/use-modals.mjs":{"*":{"id":"(rsc)/./node_modules/@mantine/modals/esm/use-modals/use-modals.mjs","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@mantine/notifications/esm/Notifications.mjs":{"*":{"id":"(rsc)/./node_modules/@mantine/notifications/esm/Notifications.mjs","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@mantine/notifications/esm/notifications.store.mjs":{"*":{"id":"(rsc)/./node_modules/@mantine/notifications/esm/notifications.store.mjs","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@mantine/notifications/styles.css":{"*":{"id":"(rsc)/./node_modules/@mantine/notifications/styles.css","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/nextjs-toploader/dist/index.js":{"*":{"id":"(rsc)/./node_modules/nextjs-toploader/dist/index.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/simplebar-react/dist/simplebar.min.css":{"*":{"id":"(rsc)/./node_modules/simplebar-react/dist/simplebar.min.css","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/components/scrollToTop/ScrollToTop.tsx":{"*":{"id":"(rsc)/./src/components/scrollToTop/ScrollToTop.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/hooks/LocaleProvider.tsx":{"*":{"id":"(rsc)/./src/hooks/LocaleProvider.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/hooks/useAuth.tsx":{"*":{"id":"(rsc)/./src/hooks/useAuth.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/style/globals.css":{"*":{"id":"(rsc)/./src/style/globals.css","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/(dashboard)/CalendarLayoutClient.tsx":{"*":{"id":"(rsc)/./src/app/(dashboard)/CalendarLayoutClient.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/(dashboard)/web/page.tsx":{"*":{"id":"(rsc)/./src/app/(dashboard)/web/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/client-page.js":{"*":{"id":"(rsc)/./node_modules/next/dist/client/components/client-page.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/client-segment.js":{"*":{"id":"(rsc)/./node_modules/next/dist/client/components/client-segment.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/error-boundary.js":{"*":{"id":"(rsc)/./node_modules/next/dist/client/components/error-boundary.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js":{"*":{"id":"(rsc)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/layout-router.js":{"*":{"id":"(rsc)/./node_modules/next/dist/client/components/layout-router.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/metadata/async-metadata.js":{"*":{"id":"(rsc)/./node_modules/next/dist/client/components/metadata/async-metadata.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js":{"*":{"id":"(rsc)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/render-from-template-context.js":{"*":{"id":"(rsc)/./node_modules/next/dist/client/components/render-from-template-context.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/contexts/DayslotGetter.tsx":{"*":{"id":"(rsc)/./src/contexts/DayslotGetter.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/(dashboard)/appointments/page.tsx":{"*":{"id":"(rsc)/./src/app/(dashboard)/appointments/page.tsx","name":"*","chunks":[],"async":false}}},"edgeRscModuleMapping":{}}