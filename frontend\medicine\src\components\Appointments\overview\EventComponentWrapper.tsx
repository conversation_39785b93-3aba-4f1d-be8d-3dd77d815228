import React from 'react';
import EventComponent from './EventComponent';
import { Appointment, AppointmentEvent } from '@/types/typesCalendarPatient';

interface EventComponentWrapperProps {
  event: Appointment;
  // Props that need to be passed from the parent component
  changeEndValuelunch: string;
  isSidebarVisible: boolean;
  activeEvent_Ids: Set<number>;
  activeIds: number[];
  activeVisits: Appointment[];
  appointments: Appointment[];
  handleRemoveLunchEvent: (eventId: string, resourceId: number | undefined) => void;
  activateAppointment: (appointment: Appointment) => void;
  setActiveVisits: React.Dispatch<React.SetStateAction<Appointment[]>>;
  setAppointments: React.Dispatch<React.SetStateAction<Appointment[]>>;
  handleLastVisit: (appointment: Appointment) => void;
  handleEditClick: (appointment: Appointment) => void;
  rescheduleAppointment: (appointment: Appointment) => void;
  openedFichepatient: () => void;
  setSelectedEvent: React.Dispatch<React.SetStateAction<AppointmentEvent | null>>;
  setShowViewModal: React.Dispatch<React.SetStateAction<boolean>>;
  handleAddAlert: (appointment: Appointment) => void;
  setAlertsOpened: React.Dispatch<React.SetStateAction<boolean>>;
  handleAddReminder: (appointment: Appointment) => void;
  removeAppointment: (appointment: Appointment) => void;
}

const EventComponentWrapper: React.FC<EventComponentWrapperProps> = (props) => {
  return <EventComponent {...props} />;
};

export default EventComponentWrapper;
