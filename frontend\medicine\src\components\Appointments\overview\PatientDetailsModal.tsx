import React from 'react';
import moment from 'moment';
import { AppointmentEvent } from '@/types/typesCalendarPatient';

interface PatientDetailsModalProps {
  opened: boolean;
  onClose: () => void;
  selectedEvent: AppointmentEvent | null;
  eventResourceId: number;
}

const PatientDetailsModal: React.FC<PatientDetailsModalProps> = ({
  opened,
  onClose,
  selectedEvent,
  eventResourceId
}) => {
  if (!opened || !selectedEvent) return null;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-white p-6 rounded-lg w-full max-w-2xl">
        <h2 className="text-xl font-bold mb-4">Patient Details</h2>

        <div className="space-y-4">
          <div className="bg-gray-50 p-4 rounded-lg">
            <h3 className="font-semibold mb-2">Personal Information {selectedEvent.patientId}</h3>
            <div className="grid grid-cols-2 gap-4">
              <div>
                <span className="font-medium">Name:</span> {selectedEvent.first_name} {selectedEvent.last_name}
              </div>
              <div>
                <span className="font-medium">Age:</span> {selectedEvent.age}
              </div>
              <div>
                <span className="font-medium">Gender:</span> {selectedEvent.gender}
              </div>
              <div>
                <span className="font-medium">Event type:</span> {selectedEvent.type}
              </div>
              <div>
                <span className="font-medium">État Civil:</span> {selectedEvent.etatCivil}
              </div>
            </div>
          </div>

          <div className="bg-gray-50 p-4 rounded-lg">
            <h3 className="font-semibold mb-2">Contact Information</h3>
            <div className="grid grid-cols-2 gap-4">
              <div>
                <span className="font-medium">Phone:</span> {selectedEvent.phone_numbers}
              </div>
              <div>
                <span className="font-medium">Email:</span> {selectedEvent.email}
              </div>
              <div>
                <span className="font-medium">Address:</span> {selectedEvent.address}
              </div>
            </div>
          </div>

          <div className="bg-gray-50 p-4 rounded-lg">
            <h3 className="font-semibold mb-2">Appointment Details</h3>
            <div className="grid grid-cols-2 gap-4">
              <div>
                <span className="font-medium">Date:</span> {moment(selectedEvent.start).format('MMM DD, YYYY')}
              </div>
              <div>
                <span className="font-medium">Time:</span> {moment(selectedEvent.start).format('HH:mm')} - {moment(selectedEvent.end).format('HH:mm')}
              </div>
              <div>
                <span className="font-medium">Doctor:</span> {selectedEvent.docteur}
              </div>
              <div>
                <span className="font-medium">Event Title:</span> {selectedEvent.event_Title}
              </div>
              <div>
                <span className="font-medium">Room:</span> {eventResourceId}
              </div>
              <div>
                <span className="font-medium">Consultation Type:</span> {selectedEvent.typeConsultation}
              </div>
              <div>
                <span className="font-medium">Social Security:</span> {selectedEvent.socialSecurity}
              </div>
              <div>
                <span className="font-medium">Comment:</span> {selectedEvent.comment}
              </div>
              <div>
                <span className="font-medium">Notes:</span> {selectedEvent.notes}
              </div>
              <div>
                <span className="font-medium">CIN:</span> {selectedEvent.cin}
              </div>
              <div>
                <span className="font-medium">Agenda:</span> {selectedEvent.etatAganda}
              </div>
            </div>
          </div>
        </div>

        <div className="flex justify-end space-x-4 mt-6">
          <button
            onClick={onClose}
            className="px-4 py-2 bg-gray-200 rounded-md hover:bg-gray-300 transition-colors"
          >
            Close
          </button>
        </div>
      </div>
    </div>
  );
};

export default PatientDetailsModal;
